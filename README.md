# 🎉 抽奖系统

一个具有iPhone风格UI的Python抽奖程序，支持奖品管理、人员批量导入和公平抽奖。

## ✨ 功能特性

### 🏆 奖品管理
- ✅ 自由设定奖品名称、数量、描述
- ✅ 实时显示剩余奖品数量
- ✅ 支持奖品的添加、编辑、删除
- ✅ 自动计算奖品概率

### 👥 人员管理
- ✅ 支持CSV文件批量导入参与者
- ✅ 支持Excel文件批量导入参与者
- ✅ 显示参与者详细信息（姓名、部门、联系方式）
- ✅ 自动标记已中奖人员

### 🎲 抽奖功能
- ✅ 单次随机抽奖
- ✅ 批量抽奖功能
- ✅ 基于概率的公平抽奖算法
- ✅ 防重复中奖机制
- ✅ 实时动画效果

### 📊 结果管理
- ✅ 查看抽奖统计信息
- ✅ 显示中奖记录列表
- ✅ 导出结果到CSV文件
- ✅ 重置抽奖功能

### 🎨 iPhone风格UI
- ✅ 现代化界面设计
- ✅ 圆角按钮和卡片样式
- ✅ iOS配色方案
- ✅ 流畅的动画效果
- ✅ 响应式布局

## 🚀 快速开始

### 环境要求
- Python 3.7+
- tkinter (Python内置)
- pandas
- openpyxl

### 安装依赖
```bash
pip install pandas openpyxl
```

### 运行程序
```bash
python lottery_app.py
```

## 📁 项目结构
```
lottery_system/
├── lottery_app.py              # 主程序入口
├── lottery_system/             # 核心模块
│   ├── __init__.py            # 包初始化
│   ├── main_app.py            # 主应用界面
│   ├── ui_components.py       # UI组件
│   ├── prize_manager.py       # 奖品管理
│   ├── participant_manager.py # 参与者管理
│   └── lottery_engine.py      # 抽奖引擎
├── sample_data/               # 示例数据
│   └── participants_sample.csv # 参与者示例文件
└── README.md                  # 说明文档
```

## 📖 使用指南

### 1. 奖品管理
1. 点击"🏆 奖品管理"选项卡
2. 在右侧输入奖品信息：
   - 奖品名称（必填）
   - 奖品数量（必填）
   - 奖品描述（可选）
3. 点击"➕ 添加奖品"按钮
4. 可以选择列表中的奖品进行编辑或删除

### 2. 人员管理
1. 点击"👥 人员管理"选项卡
2. 准备参与者数据文件：
   - CSV格式：包含"姓名"列（必须），"电话"、"邮箱"、"部门"列（可选）
   - Excel格式：同CSV格式要求
3. 点击"📄 导入CSV文件"或"📊 导入Excel文件"
4. 选择数据文件进行导入

### 3. 开始抽奖
1. 确保已添加奖品和参与者
2. 点击"🎲 开始抽奖"选项卡
3. 选择抽奖方式：
   - "🎯 开始抽奖"：单次抽奖
   - "🎪 批量抽奖"：一次抽取多个奖项
4. 查看抽奖结果和动画效果

### 4. 查看结果
1. 点击"📊 抽奖结果"选项卡
2. 查看抽奖统计信息
3. 浏览中奖记录列表
4. 点击"📤 导出结果"保存结果到文件

## 📋 数据文件格式

### CSV文件示例
```csv
姓名,电话,邮箱,部门
张三,13800138001,<EMAIL>,技术部
李四,13800138002,<EMAIL>,市场部
```

### Excel文件要求
- 支持.xlsx和.xls格式
- 第一行为列标题
- 必须包含"姓名"或"name"列
- 可选列：电话/phone、邮箱/email、部门/department

## 🎨 界面预览

程序采用iPhone风格设计，包含：
- 🎯 现代化的选项卡界面
- 🎨 iOS风格的按钮和输入框
- 📱 清爽的配色方案
- ✨ 流畅的动画效果
- 📊 直观的数据展示

## 🔧 技术特性

### 抽奖算法
- 使用Python的random模块确保随机性
- 支持基于概率的加权抽奖
- 防重复中奖机制
- 奖品数量自动管理

### 数据存储
- JSON格式存储奖品和参与者数据
- 自动保存抽奖结果
- 支持数据导入导出

### UI设计
- 基于tkinter的现代化界面
- 自定义iOS风格组件
- 响应式布局设计
- 动画效果支持

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**享受抽奖的乐趣！** 🎊
