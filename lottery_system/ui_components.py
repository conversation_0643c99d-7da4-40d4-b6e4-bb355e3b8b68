"""
UI组件模块
提供iPhone风格的UI组件
"""

import tkinter as tk
from tkinter import ttk
import math


class iOSButton(tk.Button):
    """iOS风格按钮"""
    
    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        # 默认样式配置
        default_config = {
            'font': ('SF Pro Display', 16, 'normal'),
            'relief': 'flat',
            'borderwidth': 0,
            'cursor': 'hand2',
            'pady': 12,
            'padx': 20
        }
        
        # 根据样式设置颜色
        if style == "primary":
            default_config.update({
                'bg': '#007AFF',
                'fg': 'white',
                'activebackground': '#0056CC',
                'activeforeground': 'white'
            })
        elif style == "secondary":
            default_config.update({
                'bg': '#F2F2F7',
                'fg': '#007AFF',
                'activebackground': '#E5E5EA',
                'activeforeground': '#007AFF'
            })
        elif style == "danger":
            default_config.update({
                'bg': '#FF3B30',
                'fg': 'white',
                'activebackground': '#D70015',
                'activeforeground': 'white'
            })
        elif style == "success":
            default_config.update({
                'bg': '#34C759',
                'fg': 'white',
                'activebackground': '#248A3D',
                'activeforeground': 'white'
            })
        
        # 合并用户配置
        default_config.update(kwargs)
        
        super().__init__(parent, text=text, command=command, **default_config)
        
        # 绑定悬停效果
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)
        
        self.original_bg = self['bg']
        self.hover_bg = self['activebackground']
    
    def _on_enter(self, event):
        """鼠标悬停效果"""
        self.config(bg=self.hover_bg)
    
    def _on_leave(self, event):
        """鼠标离开效果"""
        self.config(bg=self.original_bg)


class iOSFrame(tk.Frame):
    """iOS风格框架"""
    
    def __init__(self, parent, **kwargs):
        default_config = {
            'bg': '#F2F2F7',
            'relief': 'flat',
            'borderwidth': 0
        }
        default_config.update(kwargs)
        super().__init__(parent, **default_config)


class iOSLabel(tk.Label):
    """iOS风格标签"""
    
    def __init__(self, parent, text="", style="body", **kwargs):
        # 根据样式设置字体和颜色
        if style == "title":
            default_config = {
                'font': ('SF Pro Display', 28, 'bold'),
                'fg': '#000000'
            }
        elif style == "headline":
            default_config = {
                'font': ('SF Pro Display', 20, 'bold'),
                'fg': '#000000'
            }
        elif style == "subheadline":
            default_config = {
                'font': ('SF Pro Display', 16, 'normal'),
                'fg': '#3C3C43'
            }
        elif style == "body":
            default_config = {
                'font': ('SF Pro Display', 14, 'normal'),
                'fg': '#000000'
            }
        elif style == "caption":
            default_config = {
                'font': ('SF Pro Display', 12, 'normal'),
                'fg': '#8E8E93'
            }
        else:
            default_config = {
                'font': ('SF Pro Display', 14, 'normal'),
                'fg': '#000000'
            }
        
        default_config.update({
            'bg': '#F2F2F7',
            'anchor': 'w'
        })
        
        default_config.update(kwargs)
        super().__init__(parent, text=text, **default_config)


class iOSEntry(tk.Entry):
    """iOS风格输入框"""
    
    def __init__(self, parent, placeholder="", **kwargs):
        default_config = {
            'font': ('SF Pro Display', 14, 'normal'),
            'bg': 'white',
            'fg': '#000000',
            'relief': 'flat',
            'borderwidth': 1,
            'highlightthickness': 2,
            'highlightcolor': '#007AFF',
            'highlightbackground': '#C7C7CC',
            'insertbackground': '#007AFF',
            'selectbackground': '#007AFF',
            'selectforeground': 'white'
        }
        
        default_config.update(kwargs)
        super().__init__(parent, **default_config)
        
        self.placeholder = placeholder
        self.placeholder_color = '#8E8E93'
        self.normal_color = '#000000'
        
        if placeholder:
            self.insert(0, placeholder)
            self.config(fg=self.placeholder_color)
            
            self.bind('<FocusIn>', self._on_focus_in)
            self.bind('<FocusOut>', self._on_focus_out)
    
    def _on_focus_in(self, event):
        """获得焦点时的处理"""
        if self.get() == self.placeholder:
            self.delete(0, tk.END)
            self.config(fg=self.normal_color)
    
    def _on_focus_out(self, event):
        """失去焦点时的处理"""
        if not self.get():
            self.insert(0, self.placeholder)
            self.config(fg=self.placeholder_color)
    
    def get_value(self):
        """获取实际值（排除占位符）"""
        value = self.get()
        return "" if value == self.placeholder else value


class iOSListbox(tk.Listbox):
    """iOS风格列表框"""
    
    def __init__(self, parent, **kwargs):
        default_config = {
            'font': ('SF Pro Display', 14, 'normal'),
            'bg': 'white',
            'fg': '#000000',
            'selectbackground': '#007AFF',
            'selectforeground': 'white',
            'relief': 'flat',
            'borderwidth': 1,
            'highlightthickness': 0,
            'activestyle': 'none'
        }
        
        default_config.update(kwargs)
        super().__init__(parent, **default_config)


class iOSScrollbar(ttk.Scrollbar):
    """iOS风格滚动条"""
    
    def __init__(self, parent, **kwargs):
        # 创建自定义样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置滚动条样式
        style.configure("iOS.Vertical.TScrollbar",
                       background='#C7C7CC',
                       troughcolor='#F2F2F7',
                       borderwidth=0,
                       arrowcolor='#8E8E93',
                       darkcolor='#C7C7CC',
                       lightcolor='#C7C7CC')
        
        default_config = {
            'style': 'iOS.Vertical.TScrollbar'
        }
        default_config.update(kwargs)
        
        super().__init__(parent, **default_config)


class AnimatedLabel(tk.Label):
    """带动画效果的标签"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.animation_id = None
    
    def fade_in(self, duration=500):
        """淡入动画"""
        self.config(fg='#F2F2F7')  # 开始时透明
        steps = 20
        step_time = duration // steps
        
        def animate_step(step):
            if step <= steps:
                alpha = step / steps
                # 简化的透明度效果（通过颜色渐变模拟）
                gray_value = int(242 - (242 * alpha))
                color = f"#{gray_value:02x}{gray_value:02x}{gray_value:02x}"
                self.config(fg=color)
                self.animation_id = self.after(step_time, lambda: animate_step(step + 1))
            else:
                self.config(fg='#000000')  # 最终颜色
        
        animate_step(0)
    
    def pulse(self, duration=1000):
        """脉冲动画"""
        steps = 30
        step_time = duration // steps
        
        def animate_step(step):
            if step <= steps:
                # 使用正弦函数创建脉冲效果
                scale = 0.8 + 0.2 * (1 + math.sin(2 * math.pi * step / steps)) / 2
                # 简化的缩放效果（通过字体大小变化模拟）
                current_font = list(self['font'])
                current_font[1] = int(current_font[1] * scale)
                self.config(font=tuple(current_font))
                self.animation_id = self.after(step_time, lambda: animate_step(step + 1))
        
        animate_step(0)
    
    def stop_animation(self):
        """停止动画"""
        if self.animation_id:
            self.after_cancel(self.animation_id)
            self.animation_id = None
