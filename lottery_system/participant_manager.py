"""
参与者管理模块
负责抽奖人员的管理和批量导入
"""

import json
import os
import csv
import pandas as pd
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import uuid


@dataclass
class Participant:
    """参与者数据类"""
    id: str
    name: str
    phone: str = ""
    email: str = ""
    department: str = ""
    is_drawn: bool = False
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


class ParticipantManager:
    """参与者管理器"""
    
    def __init__(self, data_file: str = "participants.json"):
        self.data_file = data_file
        self.participants: List[Participant] = []
        self.load_participants()
    
    def load_participants(self):
        """从文件加载参与者数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.participants = [Participant(**p_data) for p_data in data]
            except (json.JSONDecodeError, TypeError) as e:
                print(f"加载参与者数据失败: {e}")
                self.participants = []
        else:
            self.participants = []
    
    def save_participants(self):
        """保存参与者数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump([asdict(p) for p in self.participants], f, 
                         ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存参与者数据失败: {e}")
    
    def add_participant(self, name: str, phone: str = "", email: str = "", 
                       department: str = "") -> Participant:
        """添加单个参与者"""
        participant = Participant(
            id=str(uuid.uuid4()),
            name=name,
            phone=phone,
            email=email,
            department=department,
            is_drawn=False
        )
        self.participants.append(participant)
        self.save_participants()
        return participant
    
    def update_participant(self, participant_id: str, **kwargs) -> bool:
        """更新参与者信息"""
        for participant in self.participants:
            if participant.id == participant_id:
                for key, value in kwargs.items():
                    if hasattr(participant, key):
                        setattr(participant, key, value)
                self.save_participants()
                return True
        return False
    
    def delete_participant(self, participant_id: str) -> bool:
        """删除参与者"""
        for i, participant in enumerate(self.participants):
            if participant.id == participant_id:
                del self.participants[i]
                self.save_participants()
                return True
        return False
    
    def get_participant(self, participant_id: str) -> Optional[Participant]:
        """获取指定参与者"""
        for participant in self.participants:
            if participant.id == participant_id:
                return participant
        return None
    
    def get_all_participants(self) -> List[Participant]:
        """获取所有参与者"""
        return self.participants.copy()
    
    def get_available_participants(self) -> List[Participant]:
        """获取未中奖的参与者"""
        return [p for p in self.participants if not p.is_drawn]
    
    def mark_as_drawn(self, participant_id: str) -> bool:
        """标记参与者已中奖"""
        return self.update_participant(participant_id, is_drawn=True)
    
    def reset_all_participants(self):
        """重置所有参与者的中奖状态"""
        for participant in self.participants:
            participant.is_drawn = False
        self.save_participants()
    
    def import_from_csv(self, file_path: str) -> int:
        """从CSV文件批量导入参与者"""
        try:
            imported_count = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    name = row.get('姓名', row.get('name', '')).strip()
                    if name:
                        self.add_participant(
                            name=name,
                            phone=row.get('电话', row.get('phone', '')).strip(),
                            email=row.get('邮箱', row.get('email', '')).strip(),
                            department=row.get('部门', row.get('department', '')).strip()
                        )
                        imported_count += 1
            return imported_count
        except Exception as e:
            print(f"CSV导入失败: {e}")
            return 0
    
    def import_from_excel(self, file_path: str) -> int:
        """从Excel文件批量导入参与者"""
        try:
            df = pd.read_excel(file_path)
            imported_count = 0
            
            # 尝试不同的列名组合
            name_cols = ['姓名', 'name', '名字', 'Name']
            phone_cols = ['电话', 'phone', '手机', 'Phone', '联系电话']
            email_cols = ['邮箱', 'email', 'Email', '电子邮箱']
            dept_cols = ['部门', 'department', 'Department', '科室']
            
            name_col = next((col for col in name_cols if col in df.columns), None)
            phone_col = next((col for col in phone_cols if col in df.columns), None)
            email_col = next((col for col in email_cols if col in df.columns), None)
            dept_col = next((col for col in dept_cols if col in df.columns), None)
            
            if not name_col:
                raise ValueError("未找到姓名列")
            
            for _, row in df.iterrows():
                name = str(row[name_col]).strip() if pd.notna(row[name_col]) else ""
                if name and name != 'nan':
                    self.add_participant(
                        name=name,
                        phone=str(row[phone_col]).strip() if phone_col and pd.notna(row[phone_col]) else "",
                        email=str(row[email_col]).strip() if email_col and pd.notna(row[email_col]) else "",
                        department=str(row[dept_col]).strip() if dept_col and pd.notna(row[dept_col]) else ""
                    )
                    imported_count += 1
            
            return imported_count
        except Exception as e:
            print(f"Excel导入失败: {e}")
            return 0
    
    def export_to_csv(self, file_path: str) -> bool:
        """导出参与者数据到CSV"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['姓名', '电话', '邮箱', '部门', '是否中奖'])
                for p in self.participants:
                    writer.writerow([p.name, p.phone, p.email, p.department, 
                                   '是' if p.is_drawn else '否'])
            return True
        except Exception as e:
            print(f"CSV导出失败: {e}")
            return False
    
    def clear_all_participants(self):
        """清空所有参与者"""
        self.participants = []
        self.save_participants()
