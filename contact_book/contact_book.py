import csv # 导入csv模块
import os # 导入os模块,用于检查文件是否存在

# 全局变量
contacts = []
CSV_FILE_NAME = "contacts.csv" # 定义存储数据的文件名

def save_to_file():
    """将联系人列表保存到CSV文件"""
    # ’w‘ 表示写入模式，newline='' 是为了防止写入时出现多余的空行
    try:
        with open(CSV_FILE_NAME, 'w', newline='', encoding='utf-8') as file:
            header = ['name','phone','email']
            writer = csv.DictWriter(file, fieldnames=header)
            writer.writeheader() # 写入表头
            writer.writerows(contacts) # 写入数据
    except IOError:
        print("错误：无法保存文件！")

def load_from_file():
    """从CSV文件加载联系人数据"""
    # 检查文件是否存在，如果不存在，则不执行任何操作
    if not os.path.exists(CSV_FILE_NAME):
        return
    try:
        with open(CSV_FILE_NAME, 'r', encoding='utf-8') as file:
             reader = csv.DictReader(file)
             # 使用列表推导式，更简洁
             global contacts
             contacts = [row for row in reader]
        print("通讯录数据已加载。")
    except IOError:
        print("错误：无法加载文件！")
    except csv.Error:
        print("错误：CSV文件格式不正确！")


def show_menu():
    """显示功能菜单"""
    print("\n--- 个人通讯录 ---")
    print("1. 添加联系人")
    print("2. 查看所有联系人")
    print("3. 搜索联系人")
    print("4. 删除联系人")
    print("5. 退出系统")
    print("-------------------")

def add_contact():
    """添加一个新的联系人 (添加了简单的输入验证)"""
    print("\n[添加联系人]")
    name = input("请输入姓名（不能为空）：")
    if not name:
        print("姓名不能为空，添加失败。")
        return

    phone = input("请输入电话（不能为空）：")
    if not phone:
        print("电话不能为空，添加失败。")
        return

    email = input("请输入邮箱（可以为空）：")

    # 检查姓名是否已存在
    for contact in contacts:
        if contact['name'].lower() == name.lower():
            print(f"错误：姓名为'{name}'的联系人已存在!")
            return

    contact = {'name': name, 'phone': phone, 'email': email}
    contacts.append(contact)
    print(f"联系人 ‘{name}’ 添加成功！")



def view_all_contacts():
    """查看所有联系人"""
    print("\n[所有联系人]")
    # 检查列表是否为空
    if not contacts:
        print("通讯录为空，请先添加联系人。")
        return

    # 遍历列表，打印每个联系人的信息
    # enumerate 可以同时获得索引和值，我们用 i+1 来显示序号
    for i, contact in enumerate(contacts):
        print(f"{i+1}. 姓名: {contact['name']}, 电话: {contact['phone']}, 邮箱: {contact['email']}")

def search_contact():
    """根据姓名搜索联系人"""
    print("\n[搜索联系人]")
    search_name = input("请输入要搜索的姓名：")

    found_contacts = []
    # 遍历列表，查找姓名匹配的联系人
    for contact in contacts:
        if search_name.lower() in contact['name'].lower(): # 使用lower()进行不区分大小写的搜索
            found_contacts.append(contact)

    if not found_contacts:
        print(f"未找到姓名包含 ‘{search_name}’ 的联系人。")
    else:
        print("搜索结果如下：")
        for i, contact in enumerate(found_contacts):
            print(f"{i+1}. 姓名: {contact['name']}, 电话: {contact['phone']}, 邮箱: {contact['email']}")

def delete_contact():
    """根据姓名删除联系人(优化版：增加二次确认)"""
    print("\n[删除联系人]")
    delete_name = input("请输入要删除的联系人姓名：")

    contact_to_delete = None
    # 遍历列表找到第一个匹配的联系人
    for contact in contacts:
        if contact['name'].lower() == delete_name.lower():
            contact_to_delete = contact
            break

    if contact_to_delete:
        print("找到联系人：")
        print(f"姓名: {contact_to_delete['name']}, 电话: {contact_to_delete['phone']}, 邮箱: {contact_to_delete['email']}")

        confirm = input("您确实要删除这位联系人吗？(y/n) ")

        if confirm.lower() == 'y':
            contacts.remove(contact_to_delete)
            print(f"联系人 ‘{delete_name}’ 已删除。")
        else:
            print("取消删除操作。")
    else:
        print(f"未找到姓名为 ‘{delete_name}’ 的联系人。")

def main():
    """程序主函数"""
    while True:
        show_menu()
        choice = input("请输入您的选择(1-5)：")

        action = {
            '1': add_contact,
            '2': view_all_contacts,
            '3': search_contact,
            '4': delete_contact,
        }.get(choice) # 使用字典的get方法简化if/elif

        if action:
            action()
        elif choice == '5':
            save_to_file()
            print("感谢使用，程序已退出。")
            break
        else:
            print("无效的选择，请输入1到5之间的数字。")


# 当这个文件被直接运行时，执行main（）函数
if __name__ == "__main__":
    main()
