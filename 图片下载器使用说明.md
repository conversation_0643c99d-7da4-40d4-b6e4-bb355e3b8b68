# 微信公众号图片下载器使用说明

## 功能介绍
这个Python程序可以自动从微信公众号文章页面提取并下载所有图片。

## 文件说明
- `wechat_image_downloader.py` - 主要的下载器类，包含完整功能
- `simple_image_downloader.py` - 简化版本，支持命令行参数
- `requirements.txt` - 依赖库列表

## 安装依赖
```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1：直接运行主程序
```bash
python wechat_image_downloader.py
```
这会下载默认URL中的所有图片到 `wechat_images` 文件夹。

### 方法2：使用简化版本
```bash
# 通过命令行参数指定URL
python simple_image_downloader.py "https://mp.weixin.qq.com/s/your-article-url"

# 或者直接运行，然后输入URL
python simple_image_downloader.py
```

### 方法3：在代码中使用
```python
from wechat_image_downloader import WeChatImageDownloader

# 创建下载器
downloader = WeChatImageDownloader(output_dir="my_images")

# 下载图片
url = "https://mp.weixin.qq.com/s/your-article-url"
success, total = downloader.download_all_images(url)
print(f"成功下载 {success}/{total} 张图片")
```

## 功能特点
1. **智能图片识别** - 自动识别文章中的内容图片，过滤掉表情包、图标等
2. **支持懒加载** - 处理微信的懒加载图片（data-src属性）
3. **自动重命名** - 按顺序重命名图片为 image_001.jpg, image_002.jpg 等
4. **错误处理** - 网络错误时会跳过单张图片继续下载其他图片
5. **进度显示** - 实时显示下载进度
6. **防频繁请求** - 每张图片下载间隔1秒，避免被服务器限制

## 输出结果
- 图片默认保存在 `wechat_images` 或 `downloaded_images` 文件夹中
- 图片按发现顺序命名：image_001.jpg, image_002.jpg, ...
- 控制台会显示下载进度和最终统计结果

## 注意事项
1. 请遵守网站的robots.txt和使用条款
2. 不要过于频繁地请求，避免给服务器造成压力
3. 下载的图片仅供个人学习和研究使用
4. 某些图片可能因为防盗链等原因无法下载

## 测试结果
已成功测试URL: https://mp.weixin.qq.com/s/tQUwoRwZ1wohQ4HOsn1BLg?scene=1
- 找到图片：73张
- 成功下载：73张
- 成功率：100%

## 故障排除
1. **ModuleNotFoundError** - 请确保已安装所有依赖：`pip install -r requirements.txt`
2. **网络错误** - 检查网络连接，某些图片可能需要特殊网络环境
3. **权限错误** - 确保有写入文件的权限
4. **图片无法下载** - 某些图片可能有防盗链保护，这是正常现象
