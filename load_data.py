import json
from tokenize import endpats

from movies import movies_db


def load_data():
    """从JSON文件加载数据"""
    try:
        with open("movies_data.json","r",encoding="utf-8") as f:
            data = json.load(f)
            # 验证数据格式
            if isinstance(data,list) and all(isinstance(item,dict) for item in data):
                return data
            else:
                print("❌ 数据格式不正确，将使用空数据库")
                return []
    except FileNotFoundError:
        print("❌ 数据文件不存在，将创建新文件")
        return []
    except Exception as e:
        print("❌ 加载数据出错：{e},将使用空数据库")
        return []

def save_data():
    """将数据保存到JSON文件"""
    try:
        with open("movies_data.json","w",encoding="utf-8") as f:
             json.dump(movies_db,f,ensure_ascii=False,indent=2)
        print("数据已保存")
    except Exception as e:
        print(f"保存数据出错：{e}")

#程序入口
if __name__=="__main__":
    # 启动时加载数据
    movies_db = load_data()
    print(f"已加载{len(movies_db)}条影视数据")


    # 程序结束前保存数据
    save_data()