# 🎉 多轮抽奖系统功能说明

## ✨ 新增功能概览

我已经为抽奖系统增加了强大的多轮抽奖功能，现在支持：

### 🏆 奖项管理
- **奖项级别设置**: 可以为每个奖品设置奖项名称（如"一等奖"、"二等奖"、"三等奖"）
- **轮次指定**: 可以指定奖品属于哪一轮抽奖（0表示任意轮次）
- **灵活配置**: 同一轮次可以有多个不同级别的奖项

### 🎲 多轮抽奖模式
- **轮次选择**: 可以选择特定轮次进行抽奖
- **奖项筛选**: 可以按奖项级别进行抽奖
- **批量抽奖**: 支持批量抽取指定条件的奖品
- **整轮抽奖**: 一键抽完指定轮次的所有奖品

## 📋 详细功能说明

### 1. 奖品管理增强

#### 新增字段
- **奖项级别** (`award_level`): 设置奖品的级别名称
  - 例如：一等奖、二等奖、三等奖、特等奖、纪念奖等
  - 可以自由命名，支持中文
  
- **轮次设置** (`round_number`): 指定奖品所属轮次
  - 0：任意轮次（默认值）
  - 1、2、3...：指定轮次

#### 使用示例
```
奖品名称：iPhone 15 Pro
奖项级别：一等奖
轮次：1
数量：1

奖品名称：iPad Air
奖项级别：二等奖  
轮次：1
数量：3

奖品名称：AirPods
奖项级别：三等奖
轮次：2
数量：10
```

### 2. 抽奖模式选择

#### 抽奖筛选条件
- **轮次筛选**: 选择"第1轮"、"第2轮"等
- **奖项筛选**: 选择"一等奖"、"二等奖"等
- **组合筛选**: 可以同时选择轮次和奖项级别

#### 抽奖方式
1. **单次抽奖**: 根据选择条件随机抽取1个奖品
2. **批量抽奖**: 根据选择条件批量抽取指定数量奖品
3. **整轮抽奖**: 抽完指定轮次的所有奖品（新功能）

### 3. 抽奖场景示例

#### 场景一：分轮次抽奖
```
第1轮：抽取大奖（一等奖、二等奖）
第2轮：抽取中奖（三等奖、四等奖）
第3轮：抽取纪念奖
```

#### 场景二：按奖项级别抽奖
```
先抽一等奖：1名
再抽二等奖：3名
最后抽三等奖：10名
```

#### 场景三：混合模式
```
第1轮只抽一等奖：1名
第1轮再抽二等奖：2名
第2轮抽三等奖：5名
第2轮抽纪念奖：20名
```

## 🎯 操作指南

### 1. 添加奖品
1. 进入"🏆 奖品管理"页面
2. 填写奖品信息：
   - **奖品名称**: 必填
   - **数量**: 必填
   - **轮次**: 0=任意轮次，1、2、3...=指定轮次
   - **奖项级别**: 如"一等奖"、"二等奖"（可选）
   - **描述**: 奖品详细说明（可选）
3. 点击"添加"按钮

### 2. 设置抽奖条件
1. 进入"🎲 开始抽奖"页面
2. 在"抽奖模式"区域设置：
   - **抽奖轮次**: 选择要抽奖的轮次（可选）
   - **奖项级别**: 选择要抽奖的奖项级别（可选）
   - **抽奖数量**: 设置批量抽奖的数量

### 3. 执行抽奖
- **单次抽奖**: 点击"单次抽奖"按钮
- **批量抽奖**: 点击"批量抽奖"按钮
- **整轮抽奖**: 选择轮次后点击"抽完整轮"按钮

## 🔧 技术实现

### 后端API增强
- `POST /api/lottery/draw`: 支持轮次和奖项级别参数
- `POST /api/lottery/batch`: 支持条件筛选的批量抽奖
- `POST /api/lottery/draw-round/<round>`: 抽取整轮奖品
- `POST /api/lottery/draw-award/<level>`: 抽取指定奖项级别
- `GET /api/lottery/rounds`: 获取轮次信息

### 前端界面优化
- 新增抽奖模式选择区域
- 奖品列表显示奖项级别和轮次标签
- 奖品编辑表单增加新字段
- 抽奖结果显示轮次信息

## 📊 数据结构

### 奖品数据结构
```json
{
  "id": "uuid",
  "name": "iPhone 15 Pro",
  "quantity": 1,
  "remaining": 1,
  "award_level": "一等奖",
  "round_number": 1,
  "description": "最新款iPhone",
  "probability": 0.1
}
```

### 抽奖结果数据结构
```json
{
  "id": "uuid",
  "participant_name": "张三",
  "prize_name": "iPhone 15 Pro",
  "draw_time": "2025-08-15 16:30:00",
  "round_number": 1
}
```

## 🎊 使用建议

### 1. 奖品设置建议
- 为重要奖品设置明确的奖项级别
- 合理分配轮次，避免所有奖品都在同一轮
- 设置0轮次的奖品作为"万能奖品"

### 2. 抽奖流程建议
- 先抽高级别奖项，再抽低级别奖项
- 每轮抽奖前确认参与人员和奖品设置
- 重要轮次建议使用"整轮抽奖"确保公平

### 3. 活动组织建议
- 提前规划好轮次和奖项级别
- 准备好每轮的抽奖说明
- 考虑设置互动环节增加趣味性

## 🔄 兼容性说明

- **向后兼容**: 现有奖品数据自动兼容新功能
- **默认行为**: 未设置轮次和奖项级别的奖品按原有逻辑抽奖
- **数据迁移**: 无需手动迁移，系统自动处理

---

**现在你的抽奖系统功能更加强大，可以满足各种复杂的抽奖需求！** 🎉
