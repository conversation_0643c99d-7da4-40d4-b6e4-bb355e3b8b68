# 🎉 抽奖系统使用说明

## 📋 项目概述

我为你创建了两个版本的抽奖系统：

### 1. 桌面版 (lottery_system/)
- 基于Python tkinter
- 适合本地使用
- 界面相对简单

### 2. Web版 (web_lottery/) ⭐ **推荐**
- 基于Flask + Vue.js
- 现代化Web界面
- 响应式设计，支持手机、平板、电脑
- 精美的UI和动画效果

## 🚀 快速启动 Web版（推荐）

### 方法一：自动安装启动
```bash
cd web_lottery
python install_and_run.py
```

### 方法二：手动安装
```bash
cd web_lottery
pip install Flask Flask-CORS pandas openpyxl
python start.py
```

启动后会自动打开浏览器访问 http://localhost:5000

## ✨ Web版功能特色

### 🎨 现代化界面
- **渐变色彩**: 精美的渐变背景和按钮
- **响应式设计**: 完美适配各种设备尺寸
- **流畅动画**: 抽奖结果展示动画效果
- **直观操作**: 简洁明了的用户界面

### 🏆 完整功能
1. **奖品管理**
   - 添加、编辑、删除奖品
   - 设置奖品名称、数量、描述
   - 实时显示剩余库存

2. **人员管理**
   - 支持CSV、Excel文件批量导入
   - 显示参与者详细信息
   - 自动标记中奖状态

3. **智能抽奖**
   - 单次随机抽奖
   - 批量抽奖功能
   - 防重复中奖机制
   - 精美的中奖动画

4. **结果统计**
   - 实时统计信息
   - 详细中奖记录
   - 奖品分布图表

## 📊 数据文件格式

### CSV文件示例
```csv
姓名,电话,邮箱,部门
张三,13800138001,<EMAIL>,技术部
李四,13800138002,<EMAIL>,市场部
王五,13800138003,<EMAIL>,人事部
```

### Excel文件要求
- 支持.xlsx和.xls格式
- 第一行为列标题
- 必须包含"姓名"或"name"列
- 可选列：电话、邮箱、部门

## 🎯 使用流程

### 1. 添加奖品
- 进入"奖品管理"页面
- 填写奖品名称和数量
- 可选填写描述信息
- 点击"添加"按钮

### 2. 导入参与者
- 进入"人员管理"页面
- 准备CSV或Excel文件
- 点击"批量导入"选择文件
- 系统自动解析并导入

### 3. 开始抽奖
- 进入"开始抽奖"页面
- 查看系统状态（确保有奖品和参与者）
- 选择抽奖方式：
  - 单次抽奖：随机抽取一个
  - 批量抽奖：一次抽取多个
- 观看精美的中奖动画

### 4. 查看结果
- 进入"抽奖结果"页面
- 查看统计信息和中奖记录
- 可以重置抽奖重新开始

## 🔧 技术架构

### 后端 (Python)
- **Flask**: Web框架
- **RESTful API**: 标准化接口
- **JSON存储**: 轻量级数据存储
- **文件上传**: 支持CSV/Excel导入

### 前端 (Web)
- **Vue.js 3**: 现代JavaScript框架
- **Tailwind CSS**: 实用优先的CSS框架
- **响应式设计**: 适配各种设备
- **动画效果**: 流畅的用户体验

## 📁 项目结构

```
抽奖系统/
├── lottery_system/          # 桌面版
│   ├── main_app.py         # tkinter主程序
│   ├── prize_manager.py    # 奖品管理
│   ├── participant_manager.py # 人员管理
│   └── lottery_engine.py   # 抽奖引擎
├── web_lottery/            # Web版 ⭐
│   ├── app.py             # Flask后端
│   ├── install_and_run.py # 一键启动脚本
│   ├── static/index.html  # 前端页面
│   ├── data/              # 数据存储
│   └── uploads/           # 文件上传
├── sample_data/           # 示例数据
│   └── participants_sample.csv
└── README.md             # 说明文档
```

## 🎨 界面预览

Web版界面特色：
- 🌈 **渐变色彩方案**: 现代化视觉效果
- 📱 **响应式布局**: 完美适配手机、平板、电脑
- ✨ **动画效果**: 流畅的交互体验
- 🎯 **直观操作**: 简单易用的界面设计

## 🔍 常见问题

### Q: 如何修改服务器端口？
A: 编辑`web_lottery/app.py`文件最后一行，修改port参数

### Q: 数据存储在哪里？
A: Web版数据存储在`web_lottery/data/`目录下的JSON文件中

### Q: 支持哪些文件格式？
A: 支持CSV(.csv)和Excel(.xlsx, .xls)格式

### Q: 如何备份数据？
A: 复制`data/`目录下的所有JSON文件即可

## 🚀 部署建议

### 开发环境
- 使用提供的启动脚本
- 自动开启调试模式

### 生产环境
- 使用Gunicorn或uWSGI
- 配置Nginx反向代理
- 设置HTTPS证书

## 🎊 总结

Web版抽奖系统具有以下优势：

1. **界面美观**: 现代化设计，视觉效果佳
2. **功能完整**: 涵盖抽奖系统所有必需功能
3. **易于使用**: 直观的操作界面
4. **跨平台**: 支持各种设备和浏览器
5. **可扩展**: 基于标准Web技术，易于扩展

**推荐使用Web版本，享受现代化的抽奖体验！** 🎉
