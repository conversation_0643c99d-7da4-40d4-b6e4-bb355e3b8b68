#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片合成PDF工具
将指定文件夹中的所有图片按顺序合成为一个PDF文件
"""

import os
import sys
from pathlib import Path
from PIL import Image
import argparse
from datetime import datetime


class ImagesToPDF:
    def __init__(self):
        """初始化图片转PDF工具"""
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'}
    
    def get_image_files(self, folder_path):
        """
        获取文件夹中的所有图片文件，按文件名排序
        
        Args:
            folder_path (Path): 图片文件夹路径
            
        Returns:
            list: 排序后的图片文件路径列表
        """
        image_files = []
        
        if not folder_path.exists():
            print(f"错误：文件夹 {folder_path} 不存在")
            return []
        
        # 获取所有图片文件
        for file_path in folder_path.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                image_files.append(file_path)
        
        # 按文件名排序（确保image_001.jpg, image_002.jpg的顺序）
        image_files.sort(key=lambda x: x.name)
        
        print(f"找到 {len(image_files)} 张图片")
        return image_files
    
    def process_image(self, image_path):
        """
        处理单张图片，转换为RGB格式
        
        Args:
            image_path (Path): 图片文件路径
            
        Returns:
            PIL.Image: 处理后的图片对象，失败返回None
        """
        try:
            # 打开图片
            img = Image.open(image_path)
            
            # 如果是RGBA模式，转换为RGB（PDF不支持透明度）
            if img.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')
            
            return img
            
        except Exception as e:
            print(f"处理图片失败 {image_path.name}: {e}")
            return None
    
    def resize_image_if_needed(self, img, max_width=1200, max_height=1600):
        """
        如果图片太大，按比例缩放
        
        Args:
            img (PIL.Image): 图片对象
            max_width (int): 最大宽度
            max_height (int): 最大高度
            
        Returns:
            PIL.Image: 缩放后的图片
        """
        width, height = img.size
        
        # 计算缩放比例
        width_ratio = max_width / width if width > max_width else 1
        height_ratio = max_height / height if height > max_height else 1
        ratio = min(width_ratio, height_ratio)
        
        # 如果需要缩放
        if ratio < 1:
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            print(f"图片已缩放: {width}x{height} -> {new_width}x{new_height}")
        
        return img
    
    def create_pdf(self, folder_path, output_path=None, resize=True):
        """
        将文件夹中的图片合成为PDF
        
        Args:
            folder_path (str or Path): 图片文件夹路径
            output_path (str or Path): 输出PDF文件路径，默认为None自动生成
            resize (bool): 是否自动缩放大图片
            
        Returns:
            bool: 是否成功创建PDF
        """
        folder_path = Path(folder_path)
        
        # 获取图片文件列表
        image_files = self.get_image_files(folder_path)
        if not image_files:
            print("未找到任何图片文件")
            return False
        
        # 生成输出文件名
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = folder_path.parent / f"{folder_path.name}_{timestamp}.pdf"
        else:
            output_path = Path(output_path)
        
        print(f"开始创建PDF: {output_path}")
        
        # 处理图片
        processed_images = []
        failed_count = 0
        
        for i, image_path in enumerate(image_files, 1):
            print(f"处理图片 {i}/{len(image_files)}: {image_path.name}")
            
            # 处理图片
            img = self.process_image(image_path)
            if img is None:
                failed_count += 1
                continue
            
            # 可选：缩放图片以减小PDF文件大小
            if resize:
                img = self.resize_image_if_needed(img)
            
            processed_images.append(img)
        
        if not processed_images:
            print("没有成功处理任何图片")
            return False
        
        try:
            # 创建PDF
            print("正在生成PDF文件...")
            first_image = processed_images[0]
            other_images = processed_images[1:] if len(processed_images) > 1 else []
            
            # 保存为PDF
            first_image.save(
                output_path,
                "PDF",
                save_all=True,
                append_images=other_images,
                resolution=100.0,
                quality=85
            )
            
            print(f"✅ PDF创建成功!")
            print(f"📁 输出文件: {output_path.absolute()}")
            print(f"📊 统计信息:")
            print(f"   - 总图片数: {len(image_files)}")
            print(f"   - 成功处理: {len(processed_images)}")
            print(f"   - 处理失败: {failed_count}")
            print(f"   - 文件大小: {self.get_file_size(output_path)}")
            
            return True
            
        except Exception as e:
            print(f"创建PDF失败: {e}")
            return False
    
    def get_file_size(self, file_path):
        """获取文件大小的可读格式"""
        try:
            size = file_path.stat().st_size
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024:
                    return f"{size:.1f} {unit}"
                size /= 1024
            return f"{size:.1f} TB"
        except:
            return "未知"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将图片文件夹转换为PDF')
    parser.add_argument('folder', nargs='?', default='wechat_images', 
                       help='图片文件夹路径 (默认: wechat_images)')
    parser.add_argument('-o', '--output', help='输出PDF文件路径')
    parser.add_argument('--no-resize', action='store_true', 
                       help='不自动缩放大图片')
    
    args = parser.parse_args()
    
    # 创建转换器
    converter = ImagesToPDF()
    
    # 执行转换
    success = converter.create_pdf(
        folder_path=args.folder,
        output_path=args.output,
        resize=not args.no_resize
    )
    
    if success:
        print("\n🎉 任务完成！")
    else:
        print("\n❌ 任务失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
