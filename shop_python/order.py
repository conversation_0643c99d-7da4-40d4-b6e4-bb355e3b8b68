import json
import os
from datetime import datetime

from shop_python.shop import view_products, add_product, update_stock


def create_order(products,orders):
    """创建新订单（需检查库存）"""
    view_products(products)
    order_items = []
    total_amount = 0.0

    #循环添加商品到订单
    while True:
        try:
            pid = input("请输入要购买的商品ID（输入0结束添加）：").strip()
            if pid == "0":
                break
            pid = int(pid)
            product = next((p for p in products if p["id"] == pid), None)
            if not product:
                print("❌ 未找到该商品！")
                continue

            # 输入购买数量
            while True:
                qty = int(input(f"请输入购买数量（库存：{product['stock']}）：").strip())
                if 0 < qty <= product["stock"]:
                    break
                print(f"❌ 数量必须为正数且不超过库存（{product['stock']}）！")

            # 添加到订单明细
            order_items.append({"product_id": pid, "quantity": qty})
            total_amount += product["price"] * qty
            print(f"已添加{product['name']} x{qty},当前合计:{total_amount:.2f}元")
        except ValueError:
            print("❌ 请输入有效的数字ID！")

    if not order_items:
        print("订单不能为空！")
        return

    # 生成订单ID和时间
    new_id = max((o["id"] for o in orders),default=100) + 1  #订单ID从100开始
    create_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 创建订单
    new_order = {
        "id": new_id,
        "items": order_items,
        "status": "待付款",
        "create_time": create_time,
        "total_amount": total_amount
    }
    orders.append(new_order)

    # 扣减库存
    for item in order_items:
        pid = item["product_id"]
        qty = item["quantity"]
        product = next(p for p in products if p["id"] == pid)
        product["stock"] -= qty

    print(f"✅ 已创建订单：{new_id}，合计{total_amount:.2f}元")

def view_orders(orders,products):
    """查看所有订单（支持按状态筛选）"""
    if not orders:
        print("❌ 暂无订单数据！")
        return

    # 状态筛选
    status_filter = input("请输入要筛选的状态（待付款/待发货/已发货/已完成/已取消，回车表示全部）：").strip()
    filtered = orders
    if status_filter:
        filtered = [o for o in orders if o["status"] == status_filter]
        if not filtered:
            print(f"❌ 未找到{status_filter}的订单！")
            return

    # 打印订单列表
    print(f"\n===== 订单列表 (共{len(filtered)}条)=====")
    for order in filtered:
        print(f"\n订单ID：{order['id']} | 状态：{order['status']} | 创建时间：{order['create_time']} | 总金额：{order['total_amount']:.2f}元")
        print("商品明细：")
        print(f"{'商品ID':<8}|{'商品名称':<15}|{'数量':<5}")
        print("-" * 35)
        for item in order["items"]:
            pid = item["product_id"]
            product = next(p for p in products if p["id"] == pid)
            print(f"{pid:<8}|{product['name']:<15}|{item['quantity']:<5}")

def cancel_order(orders,products):
    """取消订单（需检查状态）"""
    view_orders(orders,products)
    try:
        oid = int(input("请输入要取消的订单ID：").strip())
        order = next((o for o in orders if o["id"] == oid),None)
        if not order:
            print("❌ 未找到该订单！")
            return

        #检查是否允许取消
        if order["status"] not in ["待付款","待发货"]:
            print(f"❌ 订单状态为{order['status']},该订单状态无法取消！")
            return

        # 确认取消
        confirm = input(f"确定要取消订单{oid}吗？(y/n)").strip().lower()
        if confirm != "y":
            print("取消操作已中止！")
            return

        # 取消订单
        order["status"] = "已取消"
        print(f"✅ 已取消订单：{oid}")

        # 还原库存
        for item in order["items"]:
            pid = item["product_id"]
            qty = item["quantity"]
            product = next(p for p in products if p["id"] == pid)
            product["stock"] += qty

        print(f"✅ 订单{oid}已取消，已还原商品库存")
    except ValueError:
        print("❌ 请输入有效的数字ID！")


def create_purchase(products, purchases):
        """创建采购单(用于补货)"""
        view_products(products)
        purchase_items = []

        while  True:
            try:
                pid = input("请输入要采购的商品ID（输入0结束添加）：").strip()
                if pid == "0":
                    break
                pid = int(pid)
                product = next((p for p in products if p["id"] == pid), None)
                if not product:
                    print("❌ 未找到该商品！")
                    continue

                # 输入采购数量
                while True:
                    qty = int(input(f"请输入采购数量(当前库存{product['stock']})：").strip())
                    if qty > 0:
                        break
                    print("❌ 数量必须为正数！")

                # 添加到采购单明细
                purchase_items.append({"product_id": pid, "quantity": qty})
                print(f"已添加{product['name']} x{qty}")
            except ValueError:
                print("❌ 请输入有效的数字ID！")

        if not purchase_items:
            print("采购单不能为空！")
            return

        # 生成采购单ID和时间
        new_id = max((p["id"] for p in purchases),default=200) + 1  #采购单ID从200开始
        create_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        new_purchase = {
            "id": new_id,
            "items": purchase_items,
            "status": "待采购",
            "create_time": create_time
        }
        purchases.append(new_purchase)

        print(f"✅ 已创建采购单：{new_id}")


def receive_purchase(products,purchases):
    """采购单入库（更新库存）"""
    # 先显示所有待入库的采购单
    pending = [p for p in purchases if p["status"] in ["待采购","采购中"]]
    if not pending:
        print("❌ 暂无待入库的采购单！")
        return

    print("\n===== 待入库采购单列表 =====")
    for p in pending:
        print(f"采购单ID：{p['id']} | 创建时间：{p['create_time']}")


    try:
        pid = int(input("请输入要入库的采购单ID：").strip())
        purchase = next((p for p in purchases if p["id"] == pid),None)
        if not purchase:
            print("❌ 未找到该采购单！")
            return

        #确认入库
        confirm = input(f"确定要入库采购单{pid}吗？(y/n)").strip().lower()
        if confirm != "y":
            print("取消入库操作！")
            return

        # 更新采购单状态
        purchase["status"] = "已入库"

        # 增加库存
        for item in purchase["items"]:
            pid = item["product_id"]
            qty = item["quantity"]
            product = next(p for p in products if p["id"] == pid)
            product["stock"] += qty

        print(f"✅ 已入库采购单：{pid}")
    except ValueError:
        print("❌ 请输入有效的数字ID！")

def view_purchases(purchases,products):
    """查看所有采购单"""
    if not purchases:
        print("❌ 暂无采购单数据！")
        return

    # 状态筛选
    status_filter = input("请输入要筛选的状态（待采购/采购中/已入库/已取消，回车表示全部）：").strip()
    filetered = purchases
    if status_filter:
        filetered = [p for p in purchases if p["status"] == status_filter]
        if not filetered:
            print(f"❌ 未找到{status_filter}的采购单！")
            return

    # 打印采购单
    print(f"\n===== 采购单列表 (共{len(filetered)}条)=====")
    for p in filetered:
        print(f"\n采购单ID：{p['id']} | 状态：{p['status']} | 创建时间：{p['create_time']}")
        print("采购明细：")
        print(f"{'商品ID':<8}|{'商品名称':<15}|{'数量':<5}")
        print("-" * 35)
        for item in p["items"]:
            pid = item["product_id"]
            product = next(p for p in products if p["id"] == pid)
            print(f"{pid:<8}|{product['name']:<15}|{item['quantity']:<5}")

def ship_order(orders):
    """订单发货（更新状态为已发货）"""
    # 先显示代发货的订单
    pending_ship = [o for o in orders if o["status"] == "待发货"]
    if not pending_ship:
        print("❌ 暂无待发货的订单！")
        return

    print("\n===== 待发货订单列表 =====")
    for o in pending_ship:
        print(f"订单ID：{o['id']} | 创建时间：{o['create_time']} | 总金额：{o['total_amount']:.2f}元")

    try:
        oid = int(input("请输入要发货的订单ID：").strip())
        order = next((o for o in orders if o["id"] == oid),None)
        if not order:
            print("❌ 未找到该订单！")
            return

    # 输入物流信息（简化版）
        logistics = input("请输入物流信息：").strip()
        if not logistics:
            print("❌ 物流信息不能为空！")
            return

        # 更新订单状态
        order["status"] = "已发货"
        order["logistics"] = logistics
        print(f"✅ 已发货订单：{oid},物流单号：{logistics}")
    except ValueError:
        print("❌ 请输入有效的数字ID！")

def load_data():
    """从JSON文件加载数据"""
    if os.path.exists("ecommerce_data.json"):
        try:
            with open("ecommerce_data.json","r",encoding="utf-8") as f:
                data = json.load(f)
                return {
                    "products": data.get("products", []),
                    "orders": data.get("orders",[]),
                    "purchases": data.get("purchases",[])
                }
        except Exception as e:
            print(f"❌ 加载数据出错：{e},将使用空数据")
    return {"products":[],"orders":[],"purchases":[]}

def save_data(products,orders,purchases):
    """将数据保存到JSON文件"""
    try:
        data = {
            "products": products,
            "orders": orders,
            "purchases": purchases
        }
        with open("ecommerce_data.json","w",encoding="utf-8") as f:
             json.dump(data,f,ensure_ascii=False,indent=2)
        print("数据已保存")
    except Exception as e:
        print(f"❌ 保存数据出错：{e}")

def main():
    """主程序"""
    # 加载数据
    data = load_data()
    products = data["products"]
    orders = data["orders"]
    purchases = data["purchases"]
    print(f"数据加载完成：商品{len(products)}个，订单{len(orders)}个，采购单{len(purchases)}个")

    while True:
        print("\n===== 电商库存与订单管理系统 =====")
        print("1. 商品管理（添加/查看/更新库存）")
        print("2. 订单管理（创建/查看/取消）")
        print("3. 采购管理（创建采购单/入库/查看）")
        print("4. 订单发货")
        print("0. 退出系统")

        choice = input("请输入您的选择(0-4)：").strip()

        if choice == "1":
           #商品管理子菜单
           print("\n----- 商品管理 -----")
           print("1.添加商品")
           print("2.查看所有商品")
           print("3.更新商品库存")
           sub_choice = input("请选择（1-3):").strip()
           if sub_choice == "1":
               add_product(products)
           elif sub_choice == "2":
               view_products(products)
           elif sub_choice == "3":
               update_stock(products)
           else:
               print("❌ 请输入有效的选项（0-4）！")

        elif choice == "2":
            # 订单管理子菜单
            print("\n----- 订单管理 -----")
            print("1. 创建订单")
            print("2. 查看所有订单")
            print("3. 取消订单")
            sub_choice = input("请选择（1-3):").strip()
            if sub_choice == "1":
                create_order(products,orders)
            elif sub_choice == "2":
                view_orders(orders,products)
            elif sub_choice == "3":
                cancel_order(orders,products)
            else:
                print("❌ 请输入有效的选项（1-3）！")

        elif choice == "3":
            # 采购管理子菜单
            print("\n----- 采购管理 -----")
            print("1. 创建采购单")
            print("2. 入库采购单")
            print("3. 查看所有采购单")
            sub_choice = input("请选择（1-3):").strip()
            if sub_choice == "1":
                create_purchase(products,purchases)
            elif sub_choice == "2":
                receive_purchase(products,purchases)
            elif sub_choice == "3":
                view_purchases(purchases,products)
            else:
                print("❌ 请输入有效的选项（1-3）！")

        elif choice == "4":
            ship_order(orders)

        elif choice == "0":
            save_data(products,orders,purchases)
            print("退出系统")
            break
        else:
            print("❌ 请输入有效的选项（0-4）！")

if __name__ == "__main__":
    main()




