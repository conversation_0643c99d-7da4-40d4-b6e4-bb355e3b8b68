products = [
    {
        "id":1,
        "name":"无线鼠标",
        "price": 99.9,
        "stock": 50,   #当前库存
        "category": "电脑配件"
    },
    {
        "id":2,
        "name":"机械键盘",
        "price": 299.9,
        "stock": 30,
        "category": "电脑配件"
    },
]

orders = [
    {
        "id": 101,
        "items":[{"product_id":1,"quantity":2},{"product_id":2,"quantity":1}],
        "status": "待发货",  #状态： 待付款/待发货/已发货/已完成/已取消
        "create_time": "2025-07-29 10:59:09",
        "total": 499.7   # 订单总金额（自动计算）
    }
]

purchases = [
    {
       "id": 201,
       "items":[{"product_id":1,"quantity":10}],
       "status":"已入库", #状态：待采购/采购中/已入库/已取消
       "create_time": "2025-07-29 10:59:09",
    }
]




def add_product(products):
    """添加新商品"""
    name = input("请输入商品名称").strip()
    # 验证名称是否已存在
    if any(p["name"] == name for p in products):
        print("❌ 商品已存在！")
        return

    # 输入并验证价格
    while True:
        try:
            price = float(input("请输入商品价格").strip())
            if price > 0:
                break
            print("❌ 价格必须大于0！")
        except ValueError:
            print("❌ 价格必须是数字！")

    # 输入并验证初始库存
    while True:
        try:
            stock = int(input("请输入初始库存").strip())
            if stock >= 0:
                break
            print("❌ 库存必须大于等于0！")
        except ValueError:
            print("❌ 库存必须是整数！")

    category = input("请输入商品分类").strip()
    # 生成唯一ID（当前最大ID+1）
    new_id = max((p["id"] for p in products),default=0) + 1
    # 创建新商品字典并添加到列表
    new_product = {
        "id": new_id,
        "name": name,
        "price": price,
        "stock": stock,
        "category": category
    }
    products.append(new_product)
    print(f"✅ 已添加商品：{name}(ID:{new_id},库存:{stock})")

def view_products(products):
    """查看所有商品(按类别分组)"""
    if not products:
        print("❌ 暂无商品数据！")
        return

    # 按类别分组
    by_category = {}
    for p in products:
        category = p["category"]
        if category not in by_category:
            by_category[category] = []
        by_category[category].append(p)

    # 打印结果
    print("\n===== 商品库存列表 =====")
    for cat,items in by_category.items():
        print(f"\n 【{cat}】 ")
        print(f"{'ID':<5} | {'名称':<15} | {'价格(元)':<8} | {'库存':<5}")
        print("-" * 40)
        for p in items:
            print(f"{p['id']:<5} | {p['name']:<15} | {p['price']:<8.2f} | {p['stock']:<5}")


def update_stock(products):
    """手动更新商品库存（用于盘点）"""
    view_products(products)
    try:
        pid = int(input("请输入要更新库存的商品ID：").strip())
        product = next((p for p in products if p["id"] == pid),None)
        if not product:
            print("❌ 未找到该商品！")
            return


        while True:
            new_stock = int(input(f"请输入新的库存数量（当前：{product['stock']}）：").strip())
            if new_stock >= 0:
                break
            print("❌ 库存不能为负数！")

            product["stock"] = new_stock
            print(f"✅ 已更新商品库存：{product['name']} -> {new_stock}")
    except ValueError:
        print("请输入有效的数字")