#初始化影视数据库
from email.policy import default
from random import choice

movies_db = []

# 实例：添加一部电影到数据库
movies_db.append({
    "id": 1,
    "title":"肖申克的救赎",
    "type":"电影",
    "director":"弗兰克·德拉邦特",
    "release_year":1994,
    "status":"未看",
    "rating":None
})

# 打印数据库验证
print(movies_db)

def add_movie():
    """添加新影视资源"""
    title = input("请输入影视资源标题：").strip()

    #类型验证
    while True:
         movie_type = input("请输入类型（电影/剧集）").strip()
         if movie_type in ["电影","剧集"]:
             break
         print("❌ 类型只能是'电影'或'剧集'，请重新输入！")

    director = input("请输入导演：").strip()

    #年份认证
    while True:
        try:
            year = int(input("请输入上映年份：").strip())
            break
        except ValueError:
            print("❌ 年份必须是数字，请重新输入！")

    #生成唯一ID（当前最大ID+1）
    new_id = max((movie["id"] for movie in movies_db),default=0) + 1

    #创建新影视字典并添加到数据库
    new_movie = {
        "id":new_id,
        "title":title,
        "type":movie_type,
        "director":director,
        "release_year":year,
        "status":"未看",
        "rating":None
    }
    movies_db.append(new_movie)
    print(f"✅ 已添加影视资源：{title}(ID:{new_id})")

def view_all_movies():
    """查看所有影视资源"""
    if not movies_db:
        print("❌ 当前没有影视资源，请先添加！")
        return

    # 打印表头
    print(f"{'序号':<5}|{'标题':<20}|{'类型':<5}|{'导演':<15}|{'年份':<5}|{'状态':<8}|{'评分'}")
    print("-"*70)

    # 格式化打印每部影视
    for movie in sorted(movies_db,key=lambda x:x["id"]):
        rating_text = "未评分" if movie["rating"] is None else str(movie["rating"])
        print(f"{movie['id']:<5}|{movie['title'][:18]:<20}|{movie['type']:<5}|"
              f"{movie['director'][:13]:<15}|{movie['release_year']:<5}|"
              f"{movie['status']:<8}|{rating_text}")

# 测试功能
add_movie()  # 手动添加一个影视
view_all_movies()  #查看所有影视


def query_movies():
    """按条件查询影视资源"""
    if not movies_db:
        print("❌暂无收藏")
        return

    print("\n查询选项：")
    print("1. 按类型查询")
    print("2. 按观看状态查询")
    print("3. 按年份范围查询")
    print("4. 按标题模糊查询")
    print("5. 返回主菜单")

    while True:
        choice = input("请选择查询方式（1-5）：").strip()
        if choice not in ["1","2","3","4","5"]:
            print("❌ 请输入有效的选项（1-5）！")
            continue
        break

    if choice == "5":  #返回
        return

    results = []

    if choice == "1":    # 按类型
        while True:
            query_type = input("请输入查询的类型（电影/剧集）：").strip()
            if query_type in ["电影","剧集"]:
                break
            print("❌ 类型只能是'电影'或'剧集'，请重新输入！")
        results = [m for  m in movies_db if m["type"] == query_type]

    elif choice == "2":   # 按状态
        print("可选状态：未看 / 正在看 / 已看完")
        query_status = input("请输入查询的状态：").strip()
        results = [m for m in movies_db if m["status"] == query_status]

    elif choice == "3":    # 按年份范围
        while True:
            try:
                start_year = int(input("请输入开始年份：").strip())
                end_year = int(input("请输入结束年份：").strip())
                break
            except ValueError:
                print("❌ 年份必须是数字，请重新输入！")
        results = [m for m in movies_db if start_year <= m["release_year"] <= end_year]

    elif choice == "4":    # 按标题模糊查询
        query_title = input("请输入查询的关键字：").strip().lower()
        results = [m for m in movies_db if query_title in m["title"].lower()]

    # 显示查询结果
    if not results:
        print("❌ 未找到符合条件的影视")
        return

    print(f"\n查询结果（共{len(results)}条）：")
    view_all_movies()   #复用查看所有的函数

#测试查询功能
query_movies()


def update_movie():
    """修改影视信息"""
    if not movies_db:
        print("暂无收藏")
        return

    view_all_movies()  # 先显示所有影视

    while True:
        try:
            movie_id = int(input("请输入要修改的影视ID：").strip())
            if movie_id == 0:
                return
            # 查找对应ID的影视
            target_movie = next((m for m in movies_db if m["id"] == movie_id),None)
            if target_movie is None:
                print("❌ 未找到该ID的影视，请重新输入！")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的数字ID！")

    print(f"\n当前信息：{target_movie['title']}")
    print("(直接回车保持原值)")

    # 修改标题
    new_title = input(f"新标题（当前：{target_movie['title']}）：").strip()
    if new_title:
        target_movie["title"] = new_title

    # 修改导演
    new_director = input(f"新导演（当前：{target_movie['director']}）：").strip()
    if new_director:
        target_movie["director"] = new_director

    # 修改年份
    while True:
         new_year = input(f"新上映年份（当前：{target_movie['release_year']}）：").strip()
         if not new_year:  #不修改
             break
         try:
             target_movie["release_year"] = int(new_year)
             break
         except ValueError:
             print("❌ 年份必须是数字，请重新输入！")

    # 修改状态
    print("可选状态：未看 / 正在看 / 已看完")
    new_status = input(f"新状态（当前：{target_movie['status']}）：").strip()
    if new_status in ["未看","正在看","已看完"]:
        target_movie["status"] = new_status

    # 修改评分
    while True:
        new_rating = input(f"新评分（当前：{target_movie['rating']}）：").strip()
        if not new_rating:
            break
        try:
            rating_num = int(new_rating)
            if 0 <= rating_num <= 10:
                target_movie["rating"] = rating_num if rating_num != 0 else None
                break
            print("❌ 评分必须在0-10之间，请重新输入！")
        except ValueError:
            print("❌ 评分必须是数字，请重新输入！")

    print(f"✅ 已修改影视信息：{target_movie['title']}")

# 测试修改功能
update_movie()




def show_statistics():
    """显示影视收藏统计信息"""
    if not movies_db:
        print("暂无收藏，无法统计")
        return

    total_count = len(movies_db)
    movie_count = sum(1 for m in movies_db if m["type"] == "电影")
    series_count = sum(1 for m in movies_db if m["type"] == "剧集")

    # 状态统计
    status_counts = {
        "未看": sum(1 for m in movies_db if m["status"] == "未看"),
        "正在看": sum(1 for m in movies_db if m["status"] == "正在看"),
        "已看完": sum(1 for m in movies_db if m["status"] == "已看完")
    }

    # 评分统计（只计算有评分的）
    rated_movies = [m for m in movies_db if m["rating"] is not None]
    avg_rating = sum(m["rating"] for m in rated_movies) / len(rated_movies) if rated_movies else 0

    # 年份分布（取最近5年）
    year_counts = {}
    for movie in movies_db:
        year = movie["release_year"]
        year_counts[year] = year_counts.get(year, 0) + 1

    # 打印统计结果
    print("\n===== 影视收藏统计 =====")
    print(f"总收藏数：{total_count}")
    print(f"电影：{movie_count} ({movie_count/total_count*100:.1f}%)")
    print(f"剧集：{series_count} ({series_count / total_count * 100:.1f}%)")

    print("\n观看状态：")
    for status, count in status_counts.items():
        print(f" {status}:{count} ({count/total_count*100:.1f}%)")

    print(f"\n平均评分：{avg_rating:.1f} (基于{len(rated_movies)}部已评分影视)")

    print("\n年份分布（最多5年）：")
    for year, count in sorted(year_counts.items(), reverse=True)[:5]:
        print(f"  {year}年：{count}部")

# 测试统计功能
show_statistics()