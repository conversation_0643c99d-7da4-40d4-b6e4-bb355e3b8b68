import pandas as pd
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer
from reportlab.lib.enums import TA_JUSTIFY, TA_LEFT, TA_CENTER
from reportlab.lib.colors import navy, black
import os


def create_pdf_from_excel(excel_file_path, output_pdf_path):
    """
    根据单个Excel文件中的两个工作表生成SQL优化PDF报告。
    """
    # --- 步骤 1: 注册中文字体 ---
    # 请根据您的操作系统，取消下面对应行的注释，或者提供您系统中的任何一个中文字体文件的绝对路径。

    # Windows 示例 (使用宋体)
    font_path = 'C:/Windows/Fonts/simsun.ttc'  # 或者 simsun.ttf

    # macOS 示例 (使用黑体-简)
    # font_path = '/System/Library/Fonts/STHeiti Medium.ttc'

    # Linux 示例 (如果安装了文泉驿字体)
    # font_path = '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc'

    try:
        if not os.path.exists(font_path):
            raise FileNotFoundError(f"字体文件未在路径中找到: {font_path}")

        pdfmetrics.registerFont(TTFont('SimSun', font_path))
        font_name = 'SimSun'
        print(f"成功注册字体: {font_path}")

    except Exception as e:
        print(f"注册字体时出错: {e}")
        print("请确保您已在代码中正确设置了 font_path 变量，并指向一个有效的中文字体文件。")
        return

    # --- 步骤 2: 读取Excel文件中的工作表 ---
    try:
        # 定义要读取的工作表名称
        sheet1_name = '自运营生产环境数据库'
        sheet2_name = 'WMS数据库'

        # 读取数据到两个不同的DataFrame
        df1 = pd.read_excel(excel_file_path, sheet_name=sheet1_name)
        df2 = pd.read_excel(excel_file_path, sheet_name=sheet2_name)
        print(f"成功从 '{excel_file_path}' 读取工作表 '{sheet1_name}' 和 '{sheet2_name}'。")

    except FileNotFoundError:
        print(f"读取Excel文件时出错: 文件 '{excel_file_path}' 不存在。")
        print("请确保Excel文件与此脚本位于同一文件夹中。")
        return
    except Exception as e:
        print(f"读取Excel工作表时出错: {e}")
        print(f"请确保Excel文件中包含名为 '{sheet1_name}' 和 '{sheet2_name}' 的工作表。")
        return

    # --- 步骤 3: 定义PDF样式和结构 ---
    doc = SimpleDocTemplate(output_pdf_path, pagesize=letter)
    styles = getSampleStyleSheet()

    styles.add(ParagraphStyle(name='TitleStyle', fontName=font_name, fontSize=24, alignment=TA_CENTER, spaceAfter=20,
                              textColor=navy))
    styles.add(ParagraphStyle(name='HeaderStyle', fontName=font_name, fontSize=18, alignment=TA_LEFT, spaceAfter=10,
                              textColor=navy))
    styles.add(ParagraphStyle(name='SubHeaderStyle', fontName=font_name, fontSize=12, textColor=black, spaceBefore=10,
                              spaceAfter=5))
    styles.add(ParagraphStyle(name='BodyStyle', fontName=font_name, fontSize=10, alignment=TA_JUSTIFY, leading=14))

    story = []
    story.append(Paragraph("SQL 优化报告", styles['TitleStyle']))
    story.append(Spacer(1, 24))

    # --- 步骤 4: 填充PDF内容 ---

    # 处理第一个工作表的数据
    story.append(Paragraph(sheet1_name, styles['HeaderStyle']))
    for index, row in df1.iterrows():
        story.append(Spacer(1, 12))
        story.append(Paragraph(f"<b>记录 {index + 1}</b>", styles['SubHeaderStyle']))

        # 动态遍历所有列并添加到PDF
        for col_name in df1.columns:
            story.append(Paragraph(f"<b>{col_name}:</b>", styles['SubHeaderStyle']))
            cell_content = str(row[col_name]) if pd.notna(row[col_name]) else ""
            story.append(Paragraph(cell_content, styles['BodyStyle']))
        story.append(Spacer(1, 12))

    story.append(Spacer(1, 24))

    # 处理第二个工作表的数据
    story.append(Paragraph(sheet2_name, styles['HeaderStyle']))
    for index, row in df2.iterrows():
        story.append(Spacer(1, 12))
        story.append(Paragraph(f"<b>记录 {index + 1}</b>", styles['SubHeaderStyle']))

        # 动态遍历所有列并添加到PDF
        for col_name in df2.columns:
            story.append(Paragraph(f"<b>{col_name}:</b>", styles['SubHeaderStyle']))
            cell_content = str(row[col_name]) if pd.notna(row[col_name]) else ""
            story.append(Paragraph(cell_content, styles['BodyStyle']))
        story.append(Spacer(1, 12))

    # --- 步骤 5: 生成PDF文件 ---
    try:
        doc.build(story)
        print(f"PDF报告已成功生成: {output_pdf_path}")
    except Exception as e:
        print(f"生成PDF时发生错误: {e}")


if __name__ == '__main__':
    # 定义输入和输出文件名
    excel_file = 'SQL优化.xlsx'
    output_pdf = 'SQL_Optimization_Report.pdf'

    create_pdf_from_excel(excel_file, output_pdf)