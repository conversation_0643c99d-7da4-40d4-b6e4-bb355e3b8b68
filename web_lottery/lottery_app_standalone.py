#!/usr/bin/env python3
"""
独立运行的Web抽奖系统
适用于PyInstaller打包
"""

import os
import sys
import webbrowser
import time
import threading
from pathlib import Path

# 获取程序运行目录
if getattr(sys, 'frozen', False):
    # 如果是打包后的程序
    BASE_DIR = Path(sys.executable).parent
    TEMPLATE_DIR = BASE_DIR / 'templates'
    STATIC_DIR = BASE_DIR / 'static'
else:
    # 如果是开发环境
    BASE_DIR = Path(__file__).parent
    TEMPLATE_DIR = BASE_DIR / 'templates'
    STATIC_DIR = BASE_DIR / 'static'

# 确保数据目录存在
DATA_DIR = BASE_DIR / 'data'
UPLOAD_DIR = BASE_DIR / 'uploads'
DATA_DIR.mkdir(exist_ok=True)
UPLOAD_DIR.mkdir(exist_ok=True)

# 添加lottery_system到路径
sys.path.insert(0, str(BASE_DIR.parent / 'lottery_system'))

from flask import Flask, request, jsonify, send_from_directory, render_template_string
from flask_cors import CORS
import json
from werkzeug.utils import secure_filename
import pandas as pd

from prize_manager import PrizeManager
from participant_manager import ParticipantManager
from lottery_engine import LotteryEngine

app = Flask(__name__)
CORS(app)

# 配置
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}

# 初始化管理器
prize_manager = PrizeManager(str(DATA_DIR / 'prizes.json'))
participant_manager = ParticipantManager(str(DATA_DIR / 'participants.json'))
lottery_engine = LotteryEngine(prize_manager, participant_manager)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_html_content():
    """获取HTML内容"""
    html_file = STATIC_DIR / 'index.html'
    if html_file.exists():
        with open(html_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        # 如果文件不存在，返回简单的HTML
        return """
        <!DOCTYPE html>
        <html>
        <head><title>抽奖系统</title></head>
        <body>
            <h1>抽奖系统正在加载...</h1>
            <p>如果页面没有正常显示，请检查程序文件是否完整。</p>
        </body>
        </html>
        """

@app.route('/')
def index():
    """主页"""
    return get_html_content()

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    try:
        return send_from_directory(str(STATIC_DIR), filename)
    except:
        return "File not found", 404

# ==================== API路由 ====================

@app.route('/api/prizes', methods=['GET'])
def get_prizes():
    """获取所有奖品"""
    try:
        prizes = prize_manager.get_all_prizes()
        return jsonify({
            'success': True,
            'data': [
                {
                    'id': prize.id,
                    'name': prize.name,
                    'quantity': prize.quantity,
                    'remaining': prize.remaining,
                    'probability': prize.probability,
                    'description': prize.description,
                    'award_level': prize.award_level,
                    'round_number': prize.round_number
                }
                for prize in prizes
            ]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/prizes', methods=['POST'])
def add_prize():
    """添加奖品"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        quantity = int(data.get('quantity', 1))
        description = data.get('description', '').strip()
        award_level = data.get('award_level', '').strip()
        round_number = int(data.get('round_number', 0))

        if not name:
            return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400

        if quantity <= 0:
            return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400

        prize = prize_manager.add_prize(
            name=name,
            quantity=quantity,
            description=description,
            award_level=award_level,
            round_number=round_number
        )
        
        return jsonify({
            'success': True,
            'data': {
                'id': prize.id,
                'name': prize.name,
                'quantity': prize.quantity,
                'remaining': prize.remaining,
                'description': prize.description
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/prizes/<prize_id>', methods=['PUT'])
def update_prize(prize_id):
    """更新奖品"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        quantity = int(data.get('quantity', 1))
        description = data.get('description', '').strip()
        
        if not name:
            return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400
        
        if quantity <= 0:
            return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400
        
        success = prize_manager.update_prize(
            prize_id,
            name=name,
            quantity=quantity,
            description=description
        )
        
        if success:
            return jsonify({'success': True, 'message': '奖品更新成功'})
        else:
            return jsonify({'success': False, 'error': '奖品不存在'}), 404
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/prizes/<prize_id>', methods=['DELETE'])
def delete_prize(prize_id):
    """删除奖品"""
    try:
        success = prize_manager.delete_prize(prize_id)
        if success:
            return jsonify({'success': True, 'message': '奖品删除成功'})
        else:
            return jsonify({'success': False, 'error': '奖品不存在'}), 404
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/participants', methods=['GET'])
def get_participants():
    """获取所有参与者"""
    try:
        participants = participant_manager.get_all_participants()
        return jsonify({
            'success': True,
            'data': [
                {
                    'id': p.id,
                    'name': p.name,
                    'phone': p.phone,
                    'email': p.email,
                    'department': p.department,
                    'is_drawn': p.is_drawn
                }
                for p in participants
            ]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/participants/upload', methods=['POST'])
def upload_participants():
    """批量上传参与者"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400
        
        filename = secure_filename(file.filename)
        filepath = UPLOAD_DIR / filename
        file.save(str(filepath))
        
        # 根据文件类型导入
        if filename.endswith('.csv'):
            count = participant_manager.import_from_csv(str(filepath))
        else:
            count = participant_manager.import_from_excel(str(filepath))
        
        # 删除临时文件
        filepath.unlink()
        
        return jsonify({
            'success': True,
            'message': f'成功导入 {count} 个参与者'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/participants/clear', methods=['DELETE'])
def clear_participants():
    """清空所有参与者"""
    try:
        participant_manager.clear_all_participants()
        return jsonify({'success': True, 'message': '参与者列表已清空'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/draw', methods=['POST'])
def draw_lottery():
    """单次抽奖"""
    try:
        can_draw, message = lottery_engine.can_draw()
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400
        
        result = lottery_engine.draw_by_probability()
        if result:
            return jsonify({
                'success': True,
                'data': {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time
                }
            })
        else:
            return jsonify({'success': False, 'error': '抽奖失败'}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/batch', methods=['POST'])
def batch_draw():
    """批量抽奖"""
    try:
        data = request.get_json()
        batch_size = int(data.get('count', 1))
        
        if batch_size <= 0 or batch_size > 100:
            return jsonify({'success': False, 'error': '抽奖数量必须在1-100之间'}), 400
        
        can_draw, message = lottery_engine.can_draw()
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400
        
        results = lottery_engine.draw_batch(batch_size)
        
        return jsonify({
            'success': True,
            'data': [
                {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time
                }
                for result in results
            ]
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/reset', methods=['POST'])
def reset_lottery():
    """重置抽奖"""
    try:
        lottery_engine.reset_lottery()
        return jsonify({'success': True, 'message': '抽奖已重置'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/results', methods=['GET'])
def get_results():
    """获取抽奖结果"""
    try:
        results = lottery_engine.get_all_results()
        stats = lottery_engine.get_prize_statistics()
        winner_count = lottery_engine.get_winner_count()
        
        return jsonify({
            'success': True,
            'data': {
                'results': [
                    {
                        'id': result.id,
                        'participant_name': result.participant_name,
                        'prize_name': result.prize_name,
                        'draw_time': result.draw_time,
                        'round_number': result.round_number
                    }
                    for result in results
                ],
                'statistics': {
                    'total_draws': len(results),
                    'winner_count': winner_count,
                    'prize_stats': stats
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取系统状态"""
    try:
        total_prizes = len(prize_manager.get_all_prizes())
        available_prizes = len(prize_manager.get_available_prizes())
        total_participants = len(participant_manager.get_all_participants())
        available_participants = len(participant_manager.get_available_participants())
        
        can_draw, message = lottery_engine.can_draw()
        
        return jsonify({
            'success': True,
            'data': {
                'prizes': {
                    'total': total_prizes,
                    'available': available_prizes
                },
                'participants': {
                    'total': total_participants,
                    'available': available_participants
                },
                'can_draw': can_draw,
                'status_message': message
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass

def main():
    """主函数"""
    print("🎉 抽奖系统启动中...")
    print("📱 服务器地址: http://localhost:5000")
    print("🌐 浏览器将自动打开")
    print("⏹️  关闭此窗口停止程序")
    print("-" * 50)
    
    # 延迟打开浏览器
    threading.Timer(2.0, open_browser).start()
    
    try:
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
