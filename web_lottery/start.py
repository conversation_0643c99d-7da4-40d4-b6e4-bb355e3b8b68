#!/usr/bin/env python3
"""
Web抽奖系统启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖模块"""
    missing_modules = []
    
    try:
        import flask
    except ImportError:
        missing_modules.append("Flask")
    
    try:
        import flask_cors
    except ImportError:
        missing_modules.append("Flask-CORS")
    
    try:
        import pandas
    except ImportError:
        missing_modules.append("pandas")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    if missing_modules:
        print("❌ 缺少以下依赖模块:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n📦 请安装依赖模块:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)  # 等待服务器启动
    webbrowser.open('http://localhost:5000')

def main():
    """主函数"""
    print("🎉 Web抽奖系统启动中...")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("✅ 依赖检查通过")
    
    # 确保数据目录存在
    os.makedirs('data', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    
    print("🚀 正在启动Web服务器...")
    print("📱 服务器地址: http://localhost:5000")
    print("🌐 浏览器将自动打开，如未打开请手动访问上述地址")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    try:
        # 启动Flask应用
        from app import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
