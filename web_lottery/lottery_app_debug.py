#!/usr/bin/env python3
"""
独立运行的Web抽奖系统 - 调试版本
适用于PyInstaller打包，包含详细日志记录
"""

import os
import sys
import webbrowser
import time
import threading
import logging
import traceback
from pathlib import Path
from datetime import datetime

# 设置日志记录
def setup_logging():
    """设置日志记录"""
    try:
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的程序
            base_dir = Path(sys.executable).parent
        else:
            # 如果是开发环境
            base_dir = Path(__file__).parent
        
        # 创建日志目录
        log_dir = base_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        log_file = log_dir / f'lottery_app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        # 配置日志
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(str(log_file), encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        # 创建一个基本的logger
        logging.basicConfig(level=logging.DEBUG)
        return logging.getLogger(__name__)

# 初始化日志
logger = setup_logging()

def safe_import_and_setup():
    """安全导入和设置"""
    try:
        logger.info("开始导入模块和设置环境...")
        
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            logger.info("检测到打包环境")
            BASE_DIR = Path(sys.executable).parent
            STATIC_DIR = BASE_DIR / 'static'
        else:
            logger.info("检测到开发环境")
            BASE_DIR = Path(__file__).parent
            STATIC_DIR = BASE_DIR / 'static'
        
        logger.info(f"BASE_DIR: {BASE_DIR}")
        logger.info(f"STATIC_DIR: {STATIC_DIR}")
        
        # 确保数据目录存在
        DATA_DIR = BASE_DIR / 'data'
        UPLOAD_DIR = BASE_DIR / 'uploads'
        DATA_DIR.mkdir(exist_ok=True)
        UPLOAD_DIR.mkdir(exist_ok=True)
        logger.info(f"数据目录创建完成: {DATA_DIR}, {UPLOAD_DIR}")
        
        # 检查静态文件是否存在
        index_file = STATIC_DIR / 'index.html'
        if index_file.exists():
            logger.info(f"静态文件存在: {index_file}")
        else:
            logger.warning(f"静态文件不存在: {index_file}")
        
        # 添加lottery_system到路径
        if getattr(sys, 'frozen', False):
            # 打包环境下，模块应该已经包含在exe中
            logger.info("打包环境，跳过路径添加")
        else:
            # 开发环境
            lottery_system_path = BASE_DIR.parent / 'lottery_system'
            sys.path.insert(0, str(lottery_system_path))
            logger.info(f"添加路径: {lottery_system_path}")
        
        # 导入Flask相关模块
        logger.info("导入Flask模块...")
        from flask import Flask, request, jsonify, send_from_directory
        from flask_cors import CORS
        import json
        from werkzeug.utils import secure_filename
        
        # 导入pandas（可选）
        try:
            import pandas as pd
            logger.info("pandas导入成功")
        except ImportError as e:
            logger.warning(f"pandas导入失败: {e}")
            pd = None
        
        # 导入抽奖系统模块
        logger.info("导入抽奖系统模块...")
        try:
            from prize_manager import PrizeManager
            from participant_manager import ParticipantManager
            from lottery_engine import LotteryEngine
            logger.info("抽奖系统模块导入成功")
        except ImportError as e:
            logger.error(f"抽奖系统模块导入失败: {e}")
            logger.error(f"当前Python路径: {sys.path}")
            raise
        
        return {
            'BASE_DIR': BASE_DIR,
            'STATIC_DIR': STATIC_DIR,
            'DATA_DIR': DATA_DIR,
            'UPLOAD_DIR': UPLOAD_DIR,
            'Flask': Flask,
            'request': request,
            'jsonify': jsonify,
            'send_from_directory': send_from_directory,
            'CORS': CORS,
            'json': json,
            'secure_filename': secure_filename,
            'pd': pd,
            'PrizeManager': PrizeManager,
            'ParticipantManager': ParticipantManager,
            'LotteryEngine': LotteryEngine
        }
        
    except Exception as e:
        logger.error(f"导入和设置失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise

def create_app():
    """创建Flask应用"""
    try:
        logger.info("开始创建Flask应用...")
        
        # 安全导入和设置
        modules = safe_import_and_setup()
        
        # 创建Flask应用
        app = modules['Flask'](__name__)
        modules['CORS'](app)
        
        # 配置
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
        ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}
        
        # 获取目录
        BASE_DIR = modules['BASE_DIR']
        STATIC_DIR = modules['STATIC_DIR']
        DATA_DIR = modules['DATA_DIR']
        UPLOAD_DIR = modules['UPLOAD_DIR']
        
        # 初始化管理器
        logger.info("初始化管理器...")
        prize_manager = modules['PrizeManager'](str(DATA_DIR / 'prizes.json'))
        participant_manager = modules['ParticipantManager'](str(DATA_DIR / 'participants.json'))
        lottery_engine = modules['LotteryEngine'](prize_manager, participant_manager)
        logger.info("管理器初始化完成")
        
        def allowed_file(filename):
            """检查文件扩展名是否允许"""
            return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
        
        def get_html_content():
            """获取HTML内容"""
            try:
                html_file = STATIC_DIR / 'index.html'
                if html_file.exists():
                    with open(html_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        logger.info("HTML文件读取成功")
                        return content
                else:
                    logger.warning("HTML文件不存在，返回默认内容")
                    return """
                    <!DOCTYPE html>
                    <html>
                    <head><title>抽奖系统</title></head>
                    <body>
                        <h1>抽奖系统正在加载...</h1>
                        <p>如果页面没有正常显示，请检查程序文件是否完整。</p>
                        <p>日志文件位置: logs/目录</p>
                    </body>
                    </html>
                    """
            except Exception as e:
                logger.error(f"读取HTML文件失败: {e}")
                return f"<h1>错误</h1><p>读取HTML文件失败: {e}</p>"
        
        @app.route('/')
        def index():
            """主页"""
            try:
                logger.info("访问主页")
                return get_html_content()
            except Exception as e:
                logger.error(f"主页访问失败: {e}")
                return f"<h1>错误</h1><p>主页访问失败: {e}</p>", 500
        
        @app.route('/static/<path:filename>')
        def static_files(filename):
            """静态文件服务"""
            try:
                logger.info(f"请求静态文件: {filename}")
                return modules['send_from_directory'](str(STATIC_DIR), filename)
            except Exception as e:
                logger.error(f"静态文件访问失败: {filename}, 错误: {e}")
                return "File not found", 404
        
        @app.route('/api/test')
        def test_api():
            """测试API"""
            try:
                logger.info("测试API被调用")
                return modules['jsonify']({
                    'success': True,
                    'message': '抽奖系统运行正常',
                    'timestamp': datetime.now().isoformat(),
                    'base_dir': str(BASE_DIR),
                    'static_dir': str(STATIC_DIR)
                })
            except Exception as e:
                logger.error(f"测试API失败: {e}")
                return modules['jsonify']({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/status', methods=['GET'])
        def get_status():
            """获取系统状态"""
            try:
                logger.info("获取系统状态")
                total_prizes = len(prize_manager.get_all_prizes())
                available_prizes = len(prize_manager.get_available_prizes())
                total_participants = len(participant_manager.get_all_participants())
                available_participants = len(participant_manager.get_available_participants())
                
                can_draw, message = lottery_engine.can_draw()
                
                # 获取轮次和奖项信息
                rounds = prize_manager.get_all_rounds()
                award_levels = prize_manager.get_all_award_levels()
                
                return modules['jsonify']({
                    'success': True,
                    'data': {
                        'prizes': {
                            'total': total_prizes,
                            'available': available_prizes
                        },
                        'participants': {
                            'total': total_participants,
                            'available': available_participants
                        },
                        'can_draw': can_draw,
                        'status_message': message,
                        'rounds': rounds,
                        'award_levels': award_levels
                    }
                })
            except Exception as e:
                logger.error(f"获取系统状态失败: {e}")
                return modules['jsonify']({'success': False, 'error': str(e)}), 500
        
        logger.info("Flask应用创建完成")
        return app, BASE_DIR, STATIC_DIR, DATA_DIR, UPLOAD_DIR
        
    except Exception as e:
        logger.error(f"创建Flask应用失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise

def open_browser():
    """延迟打开浏览器"""
    try:
        time.sleep(3)
        logger.info("尝试打开浏览器")
        webbrowser.open('http://localhost:5000')
        logger.info("浏览器打开成功")
    except Exception as e:
        logger.error(f"打开浏览器失败: {e}")

def main():
    """主函数"""
    try:
        logger.info("=" * 50)
        logger.info("抽奖系统启动")
        logger.info("=" * 50)
        
        print("🎉 抽奖系统启动中...")
        print("📝 详细日志记录在 logs/ 目录中")
        
        # 创建应用
        app, BASE_DIR, STATIC_DIR, DATA_DIR, UPLOAD_DIR = create_app()
        
        print("📱 服务器地址: http://localhost:5000")
        print("🌐 浏览器将自动打开")
        print("⏹️  关闭此窗口停止程序")
        print("🔍 如有问题请查看日志文件")
        print("-" * 50)
        
        # 延迟打开浏览器
        threading.Timer(3.0, open_browser).start()
        
        # 启动Flask应用
        logger.info("启动Flask服务器...")
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("\n👋 程序已停止")
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        print(f"❌ 程序运行出错: {e}")
        print("🔍 详细错误信息请查看日志文件")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
