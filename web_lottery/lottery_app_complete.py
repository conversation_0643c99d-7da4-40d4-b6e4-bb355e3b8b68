#!/usr/bin/env python3
"""
完整的独立运行Web抽奖系统
包含所有API和错误处理
"""

import os
import sys
import webbrowser
import time
import threading
import logging
import traceback
from pathlib import Path
from datetime import datetime

# 设置日志记录
def setup_logging():
    """设置日志记录"""
    try:
        if getattr(sys, 'frozen', False):
            base_dir = Path(sys.executable).parent
        else:
            base_dir = Path(__file__).parent
        
        log_dir = base_dir / 'logs'
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f'lottery_app_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(str(log_file), encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logger = logging.getLogger(__name__)
        logger.info(f"日志系统初始化完成，日志文件: {log_file}")
        return logger
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)

logger = setup_logging()

def create_app():
    """创建完整的Flask应用"""
    try:
        logger.info("开始创建Flask应用...")
        
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            BASE_DIR = Path(sys.executable).parent
            STATIC_DIR = BASE_DIR / 'static'
        else:
            BASE_DIR = Path(__file__).parent
            STATIC_DIR = BASE_DIR / 'static'
        
        logger.info(f"BASE_DIR: {BASE_DIR}")
        logger.info(f"STATIC_DIR: {STATIC_DIR}")
        
        # 确保目录存在
        DATA_DIR = BASE_DIR / 'data'
        UPLOAD_DIR = BASE_DIR / 'uploads'
        DATA_DIR.mkdir(exist_ok=True)
        UPLOAD_DIR.mkdir(exist_ok=True)
        
        # 导入模块
        from flask import Flask, request, jsonify, send_from_directory
        from flask_cors import CORS
        import json
        from werkzeug.utils import secure_filename
        
        try:
            import pandas as pd
            logger.info("pandas导入成功")
        except ImportError:
            logger.warning("pandas导入失败")
            pd = None
        
        # 导入抽奖系统模块
        if not getattr(sys, 'frozen', False):
            sys.path.insert(0, str(BASE_DIR.parent / 'lottery_system'))
        
        from prize_manager import PrizeManager
        from participant_manager import ParticipantManager
        from lottery_engine import LotteryEngine
        
        # 创建Flask应用
        app = Flask(__name__)
        CORS(app)
        app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024
        ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}
        
        # 初始化管理器
        prize_manager = PrizeManager(str(DATA_DIR / 'prizes.json'))
        participant_manager = ParticipantManager(str(DATA_DIR / 'participants.json'))
        lottery_engine = LotteryEngine(prize_manager, participant_manager)
        
        def allowed_file(filename):
            return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
        
        def get_html_content():
            html_file = STATIC_DIR / 'index.html'
            if html_file.exists():
                with open(html_file, 'r', encoding='utf-8') as f:
                    return f.read()
            return "<h1>抽奖系统</h1><p>静态文件未找到</p>"
        
        # 路由定义
        @app.route('/')
        def index():
            return get_html_content()
        
        @app.route('/static/<path:filename>')
        def static_files(filename):
            try:
                return send_from_directory(str(STATIC_DIR), filename)
            except:
                return "File not found", 404
        
        # 奖品管理API
        @app.route('/api/prizes', methods=['GET'])
        def get_prizes():
            try:
                prizes = prize_manager.get_all_prizes()
                return jsonify({
                    'success': True,
                    'data': [
                        {
                            'id': prize.id,
                            'name': prize.name,
                            'quantity': prize.quantity,
                            'remaining': prize.remaining,
                            'probability': prize.probability,
                            'description': prize.description,
                            'award_level': prize.award_level,
                            'round_number': prize.round_number
                        }
                        for prize in prizes
                    ]
                })
            except Exception as e:
                logger.error(f"获取奖品失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/prizes', methods=['POST'])
        def add_prize():
            try:
                data = request.get_json()
                name = data.get('name', '').strip()
                quantity = int(data.get('quantity', 1))
                description = data.get('description', '').strip()
                award_level = data.get('award_level', '').strip()
                round_number = int(data.get('round_number', 0))
                
                if not name:
                    return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400
                if quantity <= 0:
                    return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400
                
                prize = prize_manager.add_prize(
                    name=name, quantity=quantity, description=description,
                    award_level=award_level, round_number=round_number
                )
                
                return jsonify({
                    'success': True,
                    'data': {
                        'id': prize.id,
                        'name': prize.name,
                        'quantity': prize.quantity,
                        'remaining': prize.remaining,
                        'description': prize.description,
                        'award_level': prize.award_level,
                        'round_number': prize.round_number
                    }
                })
            except Exception as e:
                logger.error(f"添加奖品失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/prizes/<prize_id>', methods=['PUT'])
        def update_prize(prize_id):
            try:
                data = request.get_json()
                name = data.get('name', '').strip()
                quantity = int(data.get('quantity', 1))
                description = data.get('description', '').strip()
                award_level = data.get('award_level', '').strip()
                round_number = int(data.get('round_number', 0))
                
                if not name:
                    return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400
                if quantity <= 0:
                    return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400
                
                success = prize_manager.update_prize(
                    prize_id, name=name, quantity=quantity, description=description,
                    award_level=award_level, round_number=round_number
                )
                
                if success:
                    return jsonify({'success': True, 'message': '奖品更新成功'})
                else:
                    return jsonify({'success': False, 'error': '奖品不存在'}), 404
            except Exception as e:
                logger.error(f"更新奖品失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/prizes/<prize_id>', methods=['DELETE'])
        def delete_prize(prize_id):
            try:
                success = prize_manager.delete_prize(prize_id)
                if success:
                    return jsonify({'success': True, 'message': '奖品删除成功'})
                else:
                    return jsonify({'success': False, 'error': '奖品不存在'}), 404
            except Exception as e:
                logger.error(f"删除奖品失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # 参与者管理API
        @app.route('/api/participants', methods=['GET'])
        def get_participants():
            try:
                participants = participant_manager.get_all_participants()
                return jsonify({
                    'success': True,
                    'data': [
                        {
                            'id': p.id,
                            'name': p.name,
                            'phone': p.phone,
                            'email': p.email,
                            'department': p.department,
                            'is_drawn': p.is_drawn
                        }
                        for p in participants
                    ]
                })
            except Exception as e:
                logger.error(f"获取参与者失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/participants/upload', methods=['POST'])
        def upload_participants():
            try:
                if 'file' not in request.files:
                    return jsonify({'success': False, 'error': '没有选择文件'}), 400
                
                file = request.files['file']
                if file.filename == '' or not allowed_file(file.filename):
                    return jsonify({'success': False, 'error': '文件格式不支持'}), 400
                
                filename = secure_filename(file.filename)
                filepath = UPLOAD_DIR / filename
                file.save(str(filepath))
                
                if filename.endswith('.csv'):
                    count = participant_manager.import_from_csv(str(filepath))
                else:
                    count = participant_manager.import_from_excel(str(filepath))
                
                filepath.unlink()  # 删除临时文件
                
                return jsonify({
                    'success': True,
                    'message': f'成功导入 {count} 个参与者'
                })
            except Exception as e:
                logger.error(f"上传参与者失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/participants/clear', methods=['DELETE'])
        def clear_participants():
            try:
                participant_manager.clear_all_participants()
                return jsonify({'success': True, 'message': '参与者列表已清空'})
            except Exception as e:
                logger.error(f"清空参与者失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # 抽奖API
        @app.route('/api/lottery/draw', methods=['POST'])
        def draw_lottery():
            try:
                data = request.get_json() or {}
                round_number = data.get('round_number')
                award_level = data.get('award_level')
                
                can_draw, message = lottery_engine.can_draw(round_number, award_level)
                if not can_draw:
                    return jsonify({'success': False, 'error': message}), 400
                
                result = lottery_engine.draw_by_probability(round_number, award_level)
                if result:
                    return jsonify({
                        'success': True,
                        'data': {
                            'id': result.id,
                            'participant_name': result.participant_name,
                            'prize_name': result.prize_name,
                            'draw_time': result.draw_time,
                            'round_number': result.round_number
                        }
                    })
                else:
                    return jsonify({'success': False, 'error': '抽奖失败'}), 500
            except Exception as e:
                logger.error(f"抽奖失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/lottery/batch', methods=['POST'])
        def batch_draw():
            try:
                data = request.get_json()
                batch_size = int(data.get('count', 1))
                round_number = data.get('round_number')
                award_level = data.get('award_level')
                
                if batch_size <= 0 or batch_size > 100:
                    return jsonify({'success': False, 'error': '抽奖数量必须在1-100之间'}), 400
                
                can_draw, message = lottery_engine.can_draw(round_number, award_level)
                if not can_draw:
                    return jsonify({'success': False, 'error': message}), 400
                
                results = lottery_engine.draw_batch(batch_size, round_number, award_level)
                
                return jsonify({
                    'success': True,
                    'data': [
                        {
                            'id': result.id,
                            'participant_name': result.participant_name,
                            'prize_name': result.prize_name,
                            'draw_time': result.draw_time,
                            'round_number': result.round_number
                        }
                        for result in results
                    ]
                })
            except Exception as e:
                logger.error(f"批量抽奖失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/lottery/reset', methods=['POST'])
        def reset_lottery():
            try:
                lottery_engine.reset_lottery()
                return jsonify({'success': True, 'message': '抽奖已重置'})
            except Exception as e:
                logger.error(f"重置抽奖失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        @app.route('/api/lottery/results', methods=['GET'])
        def get_results():
            try:
                results = lottery_engine.get_all_results()
                stats = lottery_engine.get_prize_statistics()
                winner_count = lottery_engine.get_winner_count()
                
                return jsonify({
                    'success': True,
                    'data': {
                        'results': [
                            {
                                'id': result.id,
                                'participant_name': result.participant_name,
                                'prize_name': result.prize_name,
                                'draw_time': result.draw_time,
                                'round_number': result.round_number
                            }
                            for result in results
                        ],
                        'statistics': {
                            'total_draws': len(results),
                            'winner_count': winner_count,
                            'prize_stats': stats
                        }
                    }
                })
            except Exception as e:
                logger.error(f"获取结果失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        # 多轮抽奖API
        @app.route('/api/lottery/rounds', methods=['GET'])
        def get_rounds():
            try:
                rounds_status = lottery_engine.get_all_rounds_status()
                award_levels = prize_manager.get_all_award_levels()

                return jsonify({
                    'success': True,
                    'data': {
                        'rounds': rounds_status,
                        'award_levels': award_levels
                    }
                })
            except Exception as e:
                logger.error(f"获取轮次信息失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @app.route('/api/lottery/draw-round/<int:round_number>', methods=['POST'])
        def draw_round(round_number):
            try:
                can_draw, message = lottery_engine.can_draw(round_number=round_number)
                if not can_draw:
                    return jsonify({'success': False, 'error': message}), 400

                results = lottery_engine.draw_round_prizes(round_number)

                return jsonify({
                    'success': True,
                    'data': [
                        {
                            'id': result.id,
                            'participant_name': result.participant_name,
                            'prize_name': result.prize_name,
                            'draw_time': result.draw_time,
                            'round_number': result.round_number
                        }
                        for result in results
                    ]
                })

            except Exception as e:
                logger.error(f"抽取轮次失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @app.route('/api/lottery/draw-award/<award_level>', methods=['POST'])
        def draw_award_level(award_level):
            try:
                data = request.get_json() or {}
                count = int(data.get('count', 1))

                if count <= 0 or count > 100:
                    return jsonify({'success': False, 'error': '抽奖数量必须在1-100之间'}), 400

                can_draw, message = lottery_engine.can_draw(award_level=award_level)
                if not can_draw:
                    return jsonify({'success': False, 'error': message}), 400

                results = lottery_engine.draw_award_level(award_level, count)

                return jsonify({
                    'success': True,
                    'data': [
                        {
                            'id': result.id,
                            'participant_name': result.participant_name,
                            'prize_name': result.prize_name,
                            'draw_time': result.draw_time,
                            'round_number': result.round_number
                        }
                        for result in results
                    ]
                })

            except Exception as e:
                logger.error(f"抽取奖项级别失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500

        @app.route('/api/status', methods=['GET'])
        def get_status():
            try:
                total_prizes = len(prize_manager.get_all_prizes())
                available_prizes = len(prize_manager.get_available_prizes())
                total_participants = len(participant_manager.get_all_participants())
                available_participants = len(participant_manager.get_available_participants())

                can_draw, message = lottery_engine.can_draw()
                rounds = prize_manager.get_all_rounds()
                award_levels = prize_manager.get_all_award_levels()

                return jsonify({
                    'success': True,
                    'data': {
                        'prizes': {'total': total_prizes, 'available': available_prizes},
                        'participants': {'total': total_participants, 'available': available_participants},
                        'can_draw': can_draw,
                        'status_message': message,
                        'rounds': rounds,
                        'award_levels': award_levels
                    }
                })
            except Exception as e:
                logger.error(f"获取状态失败: {e}")
                return jsonify({'success': False, 'error': str(e)}), 500
        
        logger.info("Flask应用创建完成")
        return app
        
    except Exception as e:
        logger.error(f"创建Flask应用失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        raise

def open_browser():
    try:
        time.sleep(3)
        webbrowser.open('http://localhost:5000')
        logger.info("浏览器打开成功")
    except Exception as e:
        logger.error(f"打开浏览器失败: {e}")

def main():
    try:
        logger.info("=" * 50)
        logger.info("抽奖系统启动")
        logger.info("=" * 50)
        
        print("🎉 抽奖系统启动中...")
        print("📝 详细日志记录在 logs/ 目录中")
        
        app = create_app()
        
        print("📱 服务器地址: http://localhost:5000")
        print("🌐 浏览器将自动打开")
        print("⏹️  关闭此窗口停止程序")
        print("-" * 50)
        
        threading.Timer(3.0, open_browser).start()
        
        logger.info("启动Flask服务器...")
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("\n👋 程序已停止")
    except Exception as e:
        logger.error(f"程序运行失败: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        print(f"❌ 程序运行出错: {e}")
        print("🔍 详细错误信息请查看日志文件")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
