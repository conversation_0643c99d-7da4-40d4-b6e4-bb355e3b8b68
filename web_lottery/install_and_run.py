#!/usr/bin/env python3
"""
自动安装依赖并启动Web抽奖系统
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def install_dependencies():
    """安装依赖模块"""
    print("📦 正在安装依赖模块...")
    try:
        # 升级pip
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])

        # 安装依赖
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'Flask'])
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'Flask-CORS'])
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pandas'])
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'openpyxl'])
        print("✅ 依赖模块安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print("请尝试手动安装: pip install Flask Flask-CORS pandas openpyxl")
        return False

def check_dependencies():
    """检查依赖模块"""
    missing_modules = []
    
    try:
        import flask
    except ImportError:
        missing_modules.append("Flask")
    
    try:
        import flask_cors
    except ImportError:
        missing_modules.append("Flask-CORS")
    
    try:
        import pandas
    except ImportError:
        missing_modules.append("pandas")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    return len(missing_modules) == 0, missing_modules

def open_browser():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:5000')
    except:
        pass

def main():
    """主函数"""
    print("🎉 Web抽奖系统启动中...")
    
    # 检查依赖
    deps_ok, missing = check_dependencies()
    if not deps_ok:
        print("❌ 缺少依赖模块，正在自动安装...")
        if not install_dependencies():
            input("按回车键退出...")
            return
    
    print("✅ 依赖检查通过")
    
    # 确保数据目录存在
    os.makedirs('data', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    
    print("🚀 正在启动Web服务器...")
    print("📱 服务器地址: http://localhost:5000")
    print("🌐 浏览器将自动打开，如未打开请手动访问上述地址")
    print("⏹️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    # 延迟打开浏览器
    Timer(3.0, open_browser).start()
    
    try:
        # 启动Flask应用
        from app import app
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
