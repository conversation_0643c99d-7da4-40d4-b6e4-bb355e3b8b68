# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 获取当前目录
current_dir = Path(SPECPATH)
lottery_system_dir = current_dir.parent / 'lottery_system'

block_cipher = None

a = Analysis(
    ['lottery_app_complete.py'],
    pathex=[
        str(current_dir),
        str(lottery_system_dir),
    ],
    binaries=[],
    datas=[
        # 包含静态文件
        ('static', 'static'),
        # 包含lottery_system模块的所有文件
        (str(lottery_system_dir / 'prize_manager.py'), '.'),
        (str(lottery_system_dir / 'participant_manager.py'), '.'),
        (str(lottery_system_dir / 'lottery_engine.py'), '.'),
        (str(lottery_system_dir / '__init__.py'), '.'),
        # 包含示例数据
        ('../sample_data', 'sample_data'),
    ],
    hiddenimports=[
        'flask',
        'flask_cors',
        'pandas',
        'openpyxl',
        'werkzeug',
        'jinja2',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'uuid',
        'json',
        'csv',
        'datetime',
        'dataclasses',
        'typing',
        'pathlib',
        'threading',
        'webbrowser',
        'time',
        'os',
        'sys',
        'logging',
        'traceback',
        'prize_manager',
        'participant_manager',
        'lottery_engine',
        'et_xmlfile',  # openpyxl依赖
        'pytz',       # pandas依赖
        'numpy',      # pandas依赖
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='抽奖系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
