"""
抽奖系统 Flask Web API
提供RESTful API接口
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import sys
import json
from werkzeug.utils import secure_filename
import pandas as pd

# 添加lottery_system到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lottery_system'))

from prize_manager import PrizeManager
from participant_manager import ParticipantManager
from lottery_engine import LotteryEngine

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 初始化管理器
prize_manager = PrizeManager('data/prizes.json')
participant_manager = ParticipantManager('data/participants.json')
lottery_engine = LotteryEngine(prize_manager, participant_manager)

# 确保数据目录存在
os.makedirs('data', exist_ok=True)


def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """主页"""
    return send_from_directory('static', 'index.html')


@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)


# ==================== 奖品管理 API ====================

@app.route('/api/prizes', methods=['GET'])
def get_prizes():
    """获取所有奖品"""
    try:
        prizes = prize_manager.get_all_prizes()
        return jsonify({
            'success': True,
            'data': [
                {
                    'id': prize.id,
                    'name': prize.name,
                    'quantity': prize.quantity,
                    'remaining': prize.remaining,
                    'probability': prize.probability,
                    'description': prize.description,
                    'award_level': prize.award_level,
                    'round_number': prize.round_number
                }
                for prize in prizes
            ]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/prizes', methods=['POST'])
def add_prize():
    """添加奖品"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        quantity = int(data.get('quantity', 1))
        description = data.get('description', '').strip()
        award_level = data.get('award_level', '').strip()
        round_number = int(data.get('round_number', 0))

        if not name:
            return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400

        if quantity <= 0:
            return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400

        prize = prize_manager.add_prize(
            name=name,
            quantity=quantity,
            description=description,
            award_level=award_level,
            round_number=round_number
        )

        return jsonify({
            'success': True,
            'data': {
                'id': prize.id,
                'name': prize.name,
                'quantity': prize.quantity,
                'remaining': prize.remaining,
                'description': prize.description,
                'award_level': prize.award_level,
                'round_number': prize.round_number
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/prizes/<prize_id>', methods=['PUT'])
def update_prize(prize_id):
    """更新奖品"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        quantity = int(data.get('quantity', 1))
        description = data.get('description', '').strip()
        award_level = data.get('award_level', '').strip()
        round_number = int(data.get('round_number', 0))

        if not name:
            return jsonify({'success': False, 'error': '奖品名称不能为空'}), 400

        if quantity <= 0:
            return jsonify({'success': False, 'error': '奖品数量必须大于0'}), 400

        success = prize_manager.update_prize(
            prize_id,
            name=name,
            quantity=quantity,
            description=description,
            award_level=award_level,
            round_number=round_number
        )

        if success:
            return jsonify({'success': True, 'message': '奖品更新成功'})
        else:
            return jsonify({'success': False, 'error': '奖品不存在'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/prizes/<prize_id>', methods=['DELETE'])
def delete_prize(prize_id):
    """删除奖品"""
    try:
        success = prize_manager.delete_prize(prize_id)
        if success:
            return jsonify({'success': True, 'message': '奖品删除成功'})
        else:
            return jsonify({'success': False, 'error': '奖品不存在'}), 404
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 参与者管理 API ====================

@app.route('/api/participants', methods=['GET'])
def get_participants():
    """获取所有参与者"""
    try:
        participants = participant_manager.get_all_participants()
        return jsonify({
            'success': True,
            'data': [
                {
                    'id': p.id,
                    'name': p.name,
                    'phone': p.phone,
                    'email': p.email,
                    'department': p.department,
                    'is_drawn': p.is_drawn
                }
                for p in participants
            ]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/participants', methods=['POST'])
def add_participant():
    """添加参与者"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        email = data.get('email', '').strip()
        department = data.get('department', '').strip()
        
        if not name:
            return jsonify({'success': False, 'error': '姓名不能为空'}), 400
        
        participant = participant_manager.add_participant(name, phone, email, department)
        
        return jsonify({
            'success': True,
            'data': {
                'id': participant.id,
                'name': participant.name,
                'phone': participant.phone,
                'email': participant.email,
                'department': participant.department,
                'is_drawn': participant.is_drawn
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/participants/upload', methods=['POST'])
def upload_participants():
    """批量上传参与者"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400
        
        filename = secure_filename(file.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)
        
        # 根据文件类型导入
        if filename.endswith('.csv'):
            count = participant_manager.import_from_csv(filepath)
        else:
            count = participant_manager.import_from_excel(filepath)
        
        # 删除临时文件
        os.remove(filepath)
        
        return jsonify({
            'success': True,
            'message': f'成功导入 {count} 个参与者'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/participants/clear', methods=['DELETE'])
def clear_participants():
    """清空所有参与者"""
    try:
        participant_manager.clear_all_participants()
        return jsonify({'success': True, 'message': '参与者列表已清空'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 抽奖 API ====================

@app.route('/api/lottery/draw', methods=['POST'])
def draw_lottery():
    """单次抽奖"""
    try:
        data = request.get_json() or {}
        round_number = data.get('round_number')
        award_level = data.get('award_level')

        can_draw, message = lottery_engine.can_draw(round_number, award_level)
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400

        result = lottery_engine.draw_by_probability(round_number, award_level)
        if result:
            return jsonify({
                'success': True,
                'data': {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time,
                    'round_number': result.round_number
                }
            })
        else:
            return jsonify({'success': False, 'error': '抽奖失败'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/lottery/batch', methods=['POST'])
def batch_draw():
    """批量抽奖"""
    try:
        data = request.get_json()
        batch_size = int(data.get('count', 1))
        round_number = data.get('round_number')
        award_level = data.get('award_level')

        if batch_size <= 0 or batch_size > 100:
            return jsonify({'success': False, 'error': '抽奖数量必须在1-100之间'}), 400

        can_draw, message = lottery_engine.can_draw(round_number, award_level)
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400

        results = lottery_engine.draw_batch(batch_size, round_number, award_level)

        return jsonify({
            'success': True,
            'data': [
                {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time,
                    'round_number': result.round_number
                }
                for result in results
            ]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/lottery/reset', methods=['POST'])
def reset_lottery():
    """重置抽奖"""
    try:
        lottery_engine.reset_lottery()
        return jsonify({'success': True, 'message': '抽奖已重置'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@app.route('/api/lottery/results', methods=['GET'])
def get_results():
    """获取抽奖结果"""
    try:
        results = lottery_engine.get_all_results()
        stats = lottery_engine.get_prize_statistics()
        winner_count = lottery_engine.get_winner_count()
        
        return jsonify({
            'success': True,
            'data': {
                'results': [
                    {
                        'id': result.id,
                        'participant_name': result.participant_name,
                        'prize_name': result.prize_name,
                        'draw_time': result.draw_time,
                        'round_number': result.round_number
                    }
                    for result in results
                ],
                'statistics': {
                    'total_draws': len(results),
                    'winner_count': winner_count,
                    'prize_stats': stats
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


# ==================== 多轮抽奖 API ====================

@app.route('/api/lottery/rounds', methods=['GET'])
def get_rounds():
    """获取所有轮次信息"""
    try:
        rounds_status = lottery_engine.get_all_rounds_status()
        award_levels = prize_manager.get_all_award_levels()

        return jsonify({
            'success': True,
            'data': {
                'rounds': rounds_status,
                'award_levels': award_levels
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/draw-round/<int:round_number>', methods=['POST'])
def draw_round(round_number):
    """抽取指定轮次的所有奖品"""
    try:
        can_draw, message = lottery_engine.can_draw(round_number=round_number)
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400

        results = lottery_engine.draw_round_prizes(round_number)

        return jsonify({
            'success': True,
            'data': [
                {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time,
                    'round_number': result.round_number
                }
                for result in results
            ]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/lottery/draw-award/<award_level>', methods=['POST'])
def draw_award_level(award_level):
    """抽取指定奖项级别的奖品"""
    try:
        data = request.get_json() or {}
        count = int(data.get('count', 1))

        if count <= 0 or count > 100:
            return jsonify({'success': False, 'error': '抽奖数量必须在1-100之间'}), 400

        can_draw, message = lottery_engine.can_draw(award_level=award_level)
        if not can_draw:
            return jsonify({'success': False, 'error': message}), 400

        results = lottery_engine.draw_award_level(award_level, count)

        return jsonify({
            'success': True,
            'data': [
                {
                    'id': result.id,
                    'participant_name': result.participant_name,
                    'prize_name': result.prize_name,
                    'draw_time': result.draw_time,
                    'round_number': result.round_number
                }
                for result in results
            ]
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取系统状态"""
    try:
        total_prizes = len(prize_manager.get_all_prizes())
        available_prizes = len(prize_manager.get_available_prizes())
        total_participants = len(participant_manager.get_all_participants())
        available_participants = len(participant_manager.get_available_participants())

        can_draw, message = lottery_engine.can_draw()

        # 获取轮次和奖项信息
        rounds = prize_manager.get_all_rounds()
        award_levels = prize_manager.get_all_award_levels()

        return jsonify({
            'success': True,
            'data': {
                'prizes': {
                    'total': total_prizes,
                    'available': available_prizes
                },
                'participants': {
                    'total': total_participants,
                    'available': available_participants
                },
                'can_draw': can_draw,
                'status_message': message,
                'rounds': rounds,
                'award_levels': award_levels
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
