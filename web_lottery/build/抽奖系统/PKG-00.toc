('C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\抽奖系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('lottery_app_complete',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\lottery_app_complete.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\Python312\\python312.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'C:\\Python312\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Python312\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('_decimal.pyd', 'C:\\Python312\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python312\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python312\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python312\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python312\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python312\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python312\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python312\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python312\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python312\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python312\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Python312\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python312\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python312\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\Python312\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\writers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\window\\indexers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\tslib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\testing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\sparse.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\sas.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\reshape.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\properties.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\parsers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\pandas_parser.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\ops.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\missing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\lib.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\json.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\join.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\interval.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\internals.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\indexing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\index.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\hashtable.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\hashing.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\groupby.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\byteswap.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\arrays.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\pandas\\_libs\\algos.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Python312\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python312\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python312\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\sqlalchemy\\cyextension\\util.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python312\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\Python312\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python312\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python312\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libcrypto-3.dll', 'C:\\Python312\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('libssl-3.dll', 'C:\\Python312\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libffi-8.dll', 'C:\\Python312\\DLLs\\libffi-8.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'C:\\Python312\\Lib\\site-packages\\pandas.libs\\msvcp140-0f2ea95580b32bcfc81c235d5751ce78.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\Python312\\Lib\\site-packages\\pywin32_system32\\pywintypes312.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Python312\\DLLs\\sqlite3.dll', 'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\PycharmProjects\\Python_test\\web_lottery\\build\\抽奖系统\\base_library.zip',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'C:\\Python312\\Lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'C:\\Python312\\Lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.4.dist-info\\entry_points.txt',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'C:\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'C:\\Python312\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.4.dist-info\\DELVEWHEEL',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\DELVEWHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.4.dist-info\\LICENSE.txt',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'C:\\Python312\\Lib\\site-packages\\setuptools\\_vendor\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.4.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\numpy-2.2.4.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Python312\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
