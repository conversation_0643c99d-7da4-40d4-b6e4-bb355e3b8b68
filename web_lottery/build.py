#!/usr/bin/env python3
"""
抽奖系统打包脚本
使用PyInstaller将程序打包成独立可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("📦 正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ PyInstaller安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller安装失败: {e}")
        return False

def clean_build():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除 {dir_name}/")
    
    # 删除.pyc文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")

    try:
        # 首先尝试简单构建调试版本
        print("📝 构建调试版本...")
        cmd_debug = [
            sys.executable, '-m', 'PyInstaller',
            '--onefile',
            '--console',
            '--name', '抽奖系统_调试版',
            '--add-data', 'static;static',
            '--add-data', '../lottery_system;.',
            '--add-data', '../sample_data;sample_data',
            'lottery_app_debug.py'
        ]

        print("执行命令:", ' '.join(cmd_debug))
        result = subprocess.run(cmd_debug, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 调试版本构建成功!")

            # 再构建正式版本
            print("📦 构建正式版本...")
            cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'lottery_app.spec']

            print("执行命令:", ' '.join(cmd))
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ 正式版本构建成功!")
                return True
            else:
                print("⚠️ 正式版本构建失败，但调试版本可用")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return True  # 调试版本可用就算成功
        else:
            print("❌ 构建失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False

    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_package():
    """创建发布包"""
    print("📦 创建发布包...")
    
    # 检查dist目录
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("❌ dist目录不存在")
        return False
    
    exe_file = dist_dir / '抽奖系统.exe'
    debug_exe_file = dist_dir / '抽奖系统_调试版.exe'

    if not exe_file.exists() and not debug_exe_file.exists():
        print("❌ 可执行文件不存在")
        return False

    # 优先使用正式版本，如果不存在则使用调试版本
    if exe_file.exists():
        main_exe = exe_file
        exe_name = '抽奖系统.exe'
    else:
        main_exe = debug_exe_file
        exe_name = '抽奖系统_调试版.exe'
    
    # 创建发布目录
    release_dir = Path('release')
    if release_dir.exists():
        shutil.rmtree(release_dir)
    
    release_dir.mkdir()
    
    # 复制可执行文件
    shutil.copy2(main_exe, release_dir / exe_name)

    # 创建数据目录
    (release_dir / 'data').mkdir()
    (release_dir / 'uploads').mkdir()

    # 复制静态文件
    static_dir = Path('static')
    if static_dir.exists():
        shutil.copytree(static_dir, release_dir / 'static')
        print(f"   复制静态文件: static/")

    # 复制示例数据
    sample_data_dir = Path('../sample_data')
    if sample_data_dir.exists():
        shutil.copytree(sample_data_dir, release_dir / 'sample_data')
        print(f"   复制示例数据: sample_data/")
    
    # 创建使用说明
    readme_content = """# 🎉 抽奖系统

## 🚀 使用方法

1. 双击"抽奖系统.exe"启动程序
2. 程序会自动打开浏览器访问抽奖系统
3. 如果浏览器没有自动打开，请手动访问: http://localhost:5000

## 📋 功能说明

### 🏆 奖品管理
- 添加、编辑、删除奖品
- 设置奖品名称、数量、描述

### 👥 人员管理  
- 支持CSV、Excel文件批量导入
- 示例文件在sample_data目录中

### 🎲 开始抽奖
- 单次抽奖：随机抽取一个奖项
- 批量抽奖：一次性抽取多个奖项

### 📊 查看结果
- 实时统计信息
- 详细中奖记录

## 📁 文件说明

- 抽奖系统.exe: 主程序
- data/: 数据存储目录
- uploads/: 文件上传临时目录  
- sample_data/: 示例数据文件

## ⚠️ 注意事项

- 首次运行可能需要几秒钟启动时间
- 关闭程序请关闭命令行窗口
- 数据会自动保存在data目录中

## 🔧 系统要求

- Windows 7/8/10/11
- 无需安装Python或其他依赖

---
享受抽奖的乐趣！🎊
"""
    
    with open(release_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ 发布包创建完成: {release_dir.absolute()}")
    print(f"📁 包含文件:")
    for item in release_dir.rglob('*'):
        if item.is_file():
            print(f"   {item.relative_to(release_dir)}")
    
    return True

def main():
    """主函数"""
    print("🎉 抽奖系统打包工具")
    print("=" * 50)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        print("❌ PyInstaller未安装")
        if not install_pyinstaller():
            print("请手动安装: pip install pyinstaller")
            return False
    
    print("✅ PyInstaller已准备就绪")
    
    # 清理构建目录
    clean_build()
    
    # 构建可执行文件
    if not build_executable():
        print("❌ 构建失败")
        return False
    
    # 创建发布包
    if not create_package():
        print("❌ 创建发布包失败")
        return False
    
    print("\n🎊 打包完成!")
    print("📦 发布文件位于 release/ 目录")
    print("💡 可以将整个release目录复制到其他电脑上运行")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        input("按回车键退出...")
