"""
抽奖引擎模块
负责抽奖的核心逻辑和算法
"""

import random
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import uuid

try:
    from .prize_manager import Prize, PrizeManager
    from .participant_manager import Participant, ParticipantManager
except ImportError:
    from prize_manager import Prize, PrizeManager
    from participant_manager import Participant, ParticipantManager


@dataclass
class LotteryResult:
    """抽奖结果数据类"""
    id: str
    participant_id: str
    participant_name: str
    prize_id: str
    prize_name: str
    draw_time: str
    round_number: int = 1


class LotteryEngine:
    """抽奖引擎"""
    
    def __init__(self, prize_manager: PrizeManager, participant_manager: ParticipantManager):
        self.prize_manager = prize_manager
        self.participant_manager = participant_manager
        self.results: List[LotteryResult] = []
        self.current_round = 1
        self.results_file = "lottery_results.json"
        self.load_results()
    
    def load_results(self):
        """加载历史抽奖结果"""
        if os.path.exists(self.results_file):
            try:
                with open(self.results_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.results = [LotteryResult(**result) for result in data]
                    if self.results:
                        self.current_round = max(r.round_number for r in self.results) + 1
            except (json.JSONDecodeError, TypeError) as e:
                print(f"加载抽奖结果失败: {e}")
                self.results = []
    
    def save_results(self):
        """保存抽奖结果"""
        try:
            with open(self.results_file, 'w', encoding='utf-8') as f:
                json.dump([asdict(result) for result in self.results], f, 
                         ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存抽奖结果失败: {e}")
    
    def draw_single_prize(self, prize_id: str) -> Optional[LotteryResult]:
        """抽取指定奖品的单个中奖者"""
        prize = self.prize_manager.get_prize(prize_id)
        if not prize or prize.remaining <= 0:
            return None
        
        available_participants = self.participant_manager.get_available_participants()
        if not available_participants:
            return None
        
        # 随机选择一个参与者
        winner = random.choice(available_participants)
        
        # 创建抽奖结果
        result = LotteryResult(
            id=str(uuid.uuid4()),
            participant_id=winner.id,
            participant_name=winner.name,
            prize_id=prize.id,
            prize_name=prize.name,
            draw_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            round_number=self.current_round
        )
        
        # 更新状态
        self.prize_manager.consume_prize(prize_id)
        self.participant_manager.mark_as_drawn(winner.id)
        
        # 保存结果
        self.results.append(result)
        self.save_results()
        
        return result
    
    def draw_multiple_prizes(self, prize_id: str, count: int) -> List[LotteryResult]:
        """抽取指定奖品的多个中奖者"""
        results = []
        for _ in range(count):
            result = self.draw_single_prize(prize_id)
            if result:
                results.append(result)
            else:
                break  # 没有更多奖品或参与者
        return results
    
    def draw_by_probability(self, round_number: int = None, award_level: str = None) -> Optional[LotteryResult]:
        """基于概率进行抽奖"""
        # 根据轮次和奖项级别筛选奖品
        if round_number is not None:
            available_prizes = self.prize_manager.get_available_prizes_by_round(round_number)
        else:
            available_prizes = self.prize_manager.get_available_prizes()

        # 如果指定了奖项级别，进一步筛选
        if award_level:
            available_prizes = [prize for prize in available_prizes if prize.award_level == award_level]

        if not available_prizes:
            return None

        available_participants = self.participant_manager.get_available_participants()
        if not available_participants:
            return None

        # 计算总概率权重
        total_weight = sum(prize.probability for prize in available_prizes)
        if total_weight <= 0:
            # 如果没有设置概率，则平均分配
            for prize in available_prizes:
                prize.probability = 1.0 / len(available_prizes)
            total_weight = 1.0

        # 使用轮盘赌算法选择奖品
        rand_num = random.uniform(0, total_weight)
        current_weight = 0
        selected_prize = None

        for prize in available_prizes:
            current_weight += prize.probability
            if rand_num <= current_weight:
                selected_prize = prize
                break

        if not selected_prize:
            selected_prize = available_prizes[-1]  # 兜底选择最后一个

        return self.draw_single_prize(selected_prize.id)
    
    def draw_batch(self, batch_size: int, round_number: int = None, award_level: str = None) -> List[LotteryResult]:
        """批量抽奖"""
        results = []
        for _ in range(batch_size):
            result = self.draw_by_probability(round_number, award_level)
            if result:
                results.append(result)
            else:
                break
        return results

    def draw_round_prizes(self, round_number: int) -> List[LotteryResult]:
        """抽取指定轮次的所有奖品"""
        results = []
        available_prizes = self.prize_manager.get_available_prizes_by_round(round_number)

        for prize in available_prizes:
            for _ in range(prize.remaining):
                result = self.draw_single_prize(prize.id)
                if result:
                    results.append(result)
                else:
                    break

        return results

    def draw_award_level(self, award_level: str, count: int = 1) -> List[LotteryResult]:
        """抽取指定奖项级别的奖品"""
        results = []
        for _ in range(count):
            result = self.draw_by_probability(award_level=award_level)
            if result:
                results.append(result)
            else:
                break
        return results
    
    def get_results_by_round(self, round_number: int) -> List[LotteryResult]:
        """获取指定轮次的抽奖结果"""
        return [r for r in self.results if r.round_number == round_number]
    
    def get_all_results(self) -> List[LotteryResult]:
        """获取所有抽奖结果"""
        return self.results.copy()
    
    def get_winner_count(self) -> int:
        """获取中奖人数"""
        return len(set(r.participant_id for r in self.results))
    
    def get_prize_statistics(self) -> Dict[str, int]:
        """获取奖品统计信息"""
        stats = {}
        for result in self.results:
            prize_name = result.prize_name
            stats[prize_name] = stats.get(prize_name, 0) + 1
        return stats
    
    def reset_lottery(self):
        """重置抽奖状态"""
        self.results = []
        self.current_round = 1
        self.prize_manager.reset_all_prizes()
        self.participant_manager.reset_all_participants()
        self.save_results()
    
    def start_new_round(self):
        """开始新一轮抽奖"""
        self.current_round += 1
    
    def can_draw(self, round_number: int = None, award_level: str = None) -> Tuple[bool, str]:
        """检查是否可以进行抽奖"""
        # 根据条件筛选奖品
        if round_number is not None:
            available_prizes = self.prize_manager.get_available_prizes_by_round(round_number)
            if not available_prizes:
                return False, f"第{round_number}轮没有可用的奖品"
        elif award_level:
            available_prizes = [p for p in self.prize_manager.get_available_prizes()
                              if p.award_level == award_level]
            if not available_prizes:
                return False, f"{award_level}没有可用的奖品"
        else:
            available_prizes = self.prize_manager.get_available_prizes()
            if not available_prizes:
                return False, "没有可用的奖品"

        available_participants = self.participant_manager.get_available_participants()
        if not available_participants:
            return False, "没有可参与抽奖的人员"

        return True, "可以进行抽奖"

    def get_round_status(self, round_number: int) -> Dict[str, Any]:
        """获取指定轮次的状态"""
        round_prizes = self.prize_manager.get_prizes_by_round(round_number)
        available_round_prizes = self.prize_manager.get_available_prizes_by_round(round_number)
        round_results = self.get_results_by_round(round_number)

        return {
            'round_number': round_number,
            'total_prizes': len(round_prizes),
            'available_prizes': len(available_round_prizes),
            'completed_draws': len(round_results),
            'prizes': [
                {
                    'id': prize.id,
                    'name': prize.name,
                    'award_level': prize.award_level,
                    'quantity': prize.quantity,
                    'remaining': prize.remaining
                }
                for prize in round_prizes
            ]
        }

    def get_all_rounds_status(self) -> List[Dict[str, Any]]:
        """获取所有轮次的状态"""
        rounds = self.prize_manager.get_all_rounds()
        return [self.get_round_status(round_num) for round_num in rounds]
    
    def export_results_to_csv(self, file_path: str) -> bool:
        """导出抽奖结果到CSV"""
        try:
            import csv
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['轮次', '中奖者', '奖品', '抽奖时间'])
                for result in self.results:
                    writer.writerow([
                        result.round_number,
                        result.participant_name,
                        result.prize_name,
                        result.draw_time
                    ])
            return True
        except Exception as e:
            print(f"导出结果失败: {e}")
            return False
