# 🎉 现代化Web抽奖系统

一个基于Flask + Vue.js的现代化抽奖系统，具有精美的UI界面和完整的功能。

## ✨ 特性亮点

### 🎨 现代化UI设计
- **响应式设计**: 完美适配桌面、平板、手机
- **渐变色彩**: 精美的渐变背景和按钮效果
- **流畅动画**: 抽奖结果展示动画效果
- **直观交互**: 简洁明了的用户界面

### 🏆 功能完整
- **奖品管理**: 添加、编辑、删除奖品，实时库存管理
- **人员管理**: 支持CSV/Excel批量导入参与者
- **智能抽奖**: 单次抽奖、批量抽奖、防重复中奖
- **结果统计**: 详细的抽奖统计和中奖记录

### 🔧 技术栈
- **后端**: Python Flask + RESTful API
- **前端**: Vue.js 3 + Tailwind CSS
- **数据**: JSON文件存储，支持数据导入导出
- **部署**: 单机部署，开箱即用

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **克隆项目**
```bash
cd web_lottery
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动服务**
```bash
python start.py
```

4. **访问系统**
- 自动打开浏览器访问 http://localhost:5000
- 或手动在浏览器中输入地址

## 📖 使用指南

### 1. 奖品管理 🏆
- 点击"奖品管理"选项卡
- 在右侧表单中输入奖品信息
- 支持设置奖品名称、数量、描述
- 可以编辑和删除已有奖品
- 实时显示剩余库存

### 2. 人员管理 👥
- 点击"人员管理"选项卡
- 支持两种导入方式：
  - **CSV文件**: 包含"姓名"列（必须），"电话"、"邮箱"、"部门"列（可选）
  - **Excel文件**: 同CSV格式要求
- 实时显示参与者状态（可参与/已中奖）

### 3. 开始抽奖 🎲
- 确保已添加奖品和参与者
- 选择抽奖方式：
  - **单次抽奖**: 随机抽取一个奖项
  - **批量抽奖**: 一次性抽取多个奖项
- 查看精美的中奖动画效果
- 支持重置抽奖功能

### 4. 查看结果 📊
- 实时统计信息展示
- 详细的中奖记录列表
- 奖品分布统计图表

## 📁 项目结构

```
web_lottery/
├── app.py                 # Flask主应用
├── start.py              # 启动脚本
├── requirements.txt      # Python依赖
├── README.md            # 说明文档
├── static/              # 静态文件
│   └── index.html       # 前端页面
├── data/                # 数据存储目录
│   ├── prizes.json      # 奖品数据
│   ├── participants.json # 参与者数据
│   └── lottery_results.json # 抽奖结果
├── uploads/             # 文件上传临时目录
└── ../lottery_system/   # 核心逻辑模块
    ├── prize_manager.py
    ├── participant_manager.py
    └── lottery_engine.py
```

## 🎯 API接口

### 奖品管理
- `GET /api/prizes` - 获取所有奖品
- `POST /api/prizes` - 添加奖品
- `PUT /api/prizes/<id>` - 更新奖品
- `DELETE /api/prizes/<id>` - 删除奖品

### 参与者管理
- `GET /api/participants` - 获取所有参与者
- `POST /api/participants` - 添加参与者
- `POST /api/participants/upload` - 批量上传
- `DELETE /api/participants/clear` - 清空列表

### 抽奖功能
- `POST /api/lottery/draw` - 单次抽奖
- `POST /api/lottery/batch` - 批量抽奖
- `POST /api/lottery/reset` - 重置抽奖
- `GET /api/lottery/results` - 获取结果

### 系统状态
- `GET /api/status` - 获取系统状态

## 📋 数据格式

### CSV文件示例
```csv
姓名,电话,邮箱,部门
张三,13800138001,<EMAIL>,技术部
李四,13800138002,<EMAIL>,市场部
```

### Excel文件要求
- 支持.xlsx和.xls格式
- 第一行为列标题
- 必须包含"姓名"或"name"列
- 可选列：电话/phone、邮箱/email、部门/department

## 🎨 界面预览

### 主要特色
- 🌈 **渐变色彩方案**: 现代化的视觉效果
- 📱 **响应式布局**: 完美适配各种设备
- ✨ **动画效果**: 流畅的交互体验
- 🎯 **直观操作**: 简单易用的界面设计

### 页面功能
1. **抽奖页面**: 大按钮设计，实时状态显示，精美结果展示
2. **奖品管理**: 列表+表单布局，实时编辑功能
3. **人员管理**: 拖拽上传，批量导入，状态标识
4. **结果统计**: 卡片式统计，详细记录列表

## 🔧 自定义配置

### 修改端口
在`app.py`文件末尾修改：
```python
app.run(debug=True, host='0.0.0.0', port=5000)  # 改为其他端口
```

### 修改数据存储路径
在`app.py`中修改：
```python
prize_manager = PrizeManager('data/prizes.json')  # 自定义路径
```

### 自定义样式
修改`static/index.html`中的CSS样式类。

## 🚀 部署建议

### 开发环境
- 使用`python start.py`启动
- 自动开启调试模式
- 支持热重载

### 生产环境
- 使用Gunicorn或uWSGI部署
- 配置Nginx反向代理
- 设置HTTPS证书

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 发起Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- Vue.js - 渐进式JavaScript框架
- Tailwind CSS - 实用优先的CSS框架
- Flask - 轻量级Python Web框架
- Font Awesome - 图标库

---

**享受现代化的抽奖体验！** 🎊
