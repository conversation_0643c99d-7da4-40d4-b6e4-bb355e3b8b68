# 🎉 抽奖系统

## 🚀 使用方法

1. 双击"抽奖系统.exe"启动程序
2. 程序会自动打开浏览器访问抽奖系统
3. 如果浏览器没有自动打开，请手动访问: http://localhost:5000

## 📋 功能说明

### 🏆 奖品管理
- 添加、编辑、删除奖品
- 设置奖品名称、数量、描述

### 👥 人员管理  
- 支持CSV、Excel文件批量导入
- 示例文件在sample_data目录中

### 🎲 开始抽奖
- 单次抽奖：随机抽取一个奖项
- 批量抽奖：一次性抽取多个奖项

### 📊 查看结果
- 实时统计信息
- 详细中奖记录

## 📁 文件说明

- 抽奖系统.exe: 主程序
- data/: 数据存储目录
- uploads/: 文件上传临时目录  
- sample_data/: 示例数据文件

## ⚠️ 注意事项

- 首次运行可能需要几秒钟启动时间
- 关闭程序请关闭命令行窗口
- 数据会自动保存在data目录中

## 🔧 系统要求

- Windows 7/8/10/11
- 无需安装Python或其他依赖

---
享受抽奖的乐趣！🎊
