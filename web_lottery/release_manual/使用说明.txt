🎉 抽奖系统使用说明

===============================================
📋 系统简介
===============================================

这是一个功能完整的多轮抽奖系统，支持：
✅ 奖品管理 - 设置奖品名称、数量、奖项级别、轮次
✅ 人员管理 - 批量导入参与者（CSV/Excel）
✅ 多轮抽奖 - 支持按轮次、奖项级别抽奖
✅ 结果统计 - 详细的抽奖记录和统计信息
✅ 现代化界面 - 基于Web的美观界面

===============================================
🚀 快速开始
===============================================

1. 双击"抽奖系统.exe"启动程序
2. 程序会自动打开浏览器访问 http://localhost:5000
3. 如果浏览器没有自动打开，请手动访问上述地址

===============================================
📖 详细使用步骤
===============================================

第一步：添加奖品
1. 点击"🏆 奖品管理"选项卡
2. 在右侧表单中填写：
   - 奖品名称：如"iPhone 15 Pro"
   - 数量：奖品数量
   - 轮次：0=任意轮次，1、2、3...=指定轮次
   - 奖项级别：如"一等奖"、"二等奖"（可选）
   - 描述：奖品详细说明（可选）
3. 点击"添加"按钮

第二步：导入参与者
1. 点击"👥 人员管理"选项卡
2. 准备数据文件：
   - 使用sample_data目录中的示例文件作为模板
   - CSV格式：包含"姓名"列（必须），"电话"、"邮箱"、"部门"列（可选）
   - Excel格式：同CSV要求
3. 点击"批量导入"选择文件上传

第三步：设置抽奖条件
1. 点击"🎲 开始抽奖"选项卡
2. 在"抽奖模式"区域设置：
   - 抽奖轮次：选择要抽奖的轮次（可选）
   - 奖项级别：选择要抽奖的奖项级别（可选）
   - 抽奖数量：设置批量抽奖的数量

第四步：执行抽奖
- 单次抽奖：点击"单次抽奖"按钮
- 批量抽奖：点击"批量抽奖"按钮
- 整轮抽奖：选择轮次后点击"抽完整轮"按钮

第五步：查看结果
1. 点击"📊 抽奖结果"选项卡
2. 查看抽奖统计信息和中奖记录

===============================================
🎯 抽奖场景示例
===============================================

场景一：分轮次抽奖
- 第1轮：抽取大奖（一等奖1名、二等奖3名）
- 第2轮：抽取中奖（三等奖10名）
- 第3轮：抽取纪念奖（20名）

场景二：按奖项级别抽奖
- 先抽一等奖：1名
- 再抽二等奖：3名
- 最后抽三等奖：10名

===============================================
📁 文件说明
===============================================

抽奖系统.exe    - 主程序文件
static/         - 网页界面文件
data/           - 数据存储目录（自动创建）
uploads/        - 文件上传临时目录
sample_data/    - 示例数据文件
logs/           - 日志文件目录（运行时创建）
使用说明.txt    - 本说明文件

===============================================
🔧 数据文件格式
===============================================

CSV文件示例：
姓名,电话,邮箱,部门
张三,13800138001,<EMAIL>,技术部
李四,13800138002,<EMAIL>,市场部

Excel文件要求：
- 支持.xlsx和.xls格式
- 第一行为列标题
- 必须包含"姓名"或"name"列
- 可选列：电话/phone、邮箱/email、部门/department

===============================================
⚠️ 注意事项
===============================================

1. 首次运行可能需要几秒钟启动时间
2. 关闭程序请关闭命令行窗口
3. 数据会自动保存在data目录中
4. 如遇问题请查看logs目录中的日志文件
5. 确保防火墙允许程序访问网络（本地访问）

===============================================
🔍 故障排除
===============================================

问题：程序启动失败
解决：查看logs目录中的日志文件，了解具体错误信息

问题：浏览器无法访问
解决：手动在浏览器中输入 http://localhost:5000

问题：文件上传失败
解决：检查文件格式是否正确，确保包含必要的列

问题：抽奖无法进行
解决：确保已添加奖品和参与者，检查奖品库存

===============================================
📞 技术支持
===============================================

如有其他问题，请查看日志文件获取详细错误信息。

===============================================
🎊 享受抽奖的乐趣！
===============================================
