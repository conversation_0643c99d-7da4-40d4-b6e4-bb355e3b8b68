<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎉 抽奖系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }
        
        .animate-bounce-in {
            animation: bounceIn 0.6s ease-out;
        }
        
        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .animate-pulse-slow {
            animation: pulse 2s infinite;
        }
        
        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .result-card {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: 2px solid #f59e0b;
        }
        
        .winner-animation {
            animation: celebration 1s ease-in-out;
        }
        
        @keyframes celebration {
            0%, 100% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.1) rotate(-5deg); }
            75% { transform: scale(1.1) rotate(5deg); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div id="app">
        <!-- 顶部导航 -->
        <nav class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold">🎉 抽奖系统</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-sm">
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                🏆 奖品: {{ status.prizes.available }}/{{ status.prizes.total }}
                            </span>
                            <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full ml-2">
                                👥 参与者: {{ status.participants.available }}/{{ status.participants.total }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 主要内容 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 选项卡导航 -->
            <div class="flex space-x-1 bg-white p-1 rounded-xl card-shadow mb-8">
                <button 
                    v-for="tab in tabs" 
                    :key="tab.id"
                    @click="activeTab = tab.id"
                    :class="['flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200', 
                             activeTab === tab.id ? 'tab-active' : 'text-gray-600 hover:text-gray-800']">
                    {{ tab.icon }} {{ tab.name }}
                </button>
            </div>

            <!-- 抽奖页面 -->
            <div v-if="activeTab === 'lottery'" class="space-y-8">
                <!-- 状态卡片 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="text-center">
                        <div class="text-lg font-medium text-gray-600 mb-2">系统状态</div>
                        <div :class="['text-2xl font-bold', status.can_draw ? 'text-green-600' : 'text-red-600']">
                            {{ status.status_message }}
                        </div>
                    </div>
                </div>

                <!-- 抽奖模式选择 -->
                <div class="bg-white rounded-xl card-shadow p-6 mb-6">
                    <h3 class="text-lg font-bold mb-4">抽奖模式</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700">抽奖轮次</label>
                            <select v-model="selectedRound" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">所有轮次</option>
                                <option v-for="round in status.rounds" :key="round" :value="round">第{{ round }}轮</option>
                            </select>
                        </div>

                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700">奖项级别</label>
                            <select v-model="selectedAwardLevel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option value="">所有奖项</option>
                                <option v-for="level in status.award_levels" :key="level" :value="level">{{ level }}</option>
                            </select>
                        </div>

                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700">抽奖数量</label>
                            <input v-model.number="drawCount" type="number" min="1" max="100" value="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                </div>

                <!-- 抽奖控制 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex flex-wrap gap-4 justify-center">
                        <button
                            @click="drawSingle"
                            :disabled="!status.can_draw || loading"
                            class="btn-primary text-white px-8 py-4 rounded-xl font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-dice mr-2"></i>
                            {{ loading ? '抽奖中...' : '单次抽奖' }}
                        </button>

                        <button
                            @click="drawBatch"
                            :disabled="!status.can_draw || loading"
                            class="btn-secondary text-gray-800 px-8 py-4 rounded-xl font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-layer-group mr-2"></i>
                            批量抽奖
                        </button>

                        <button
                            v-if="selectedRound"
                            @click="drawEntireRound"
                            :disabled="!status.can_draw || loading"
                            class="btn-success text-white px-8 py-4 rounded-xl font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-trophy mr-2"></i>
                            抽完整轮
                        </button>

                        <button
                            @click="resetLottery"
                            class="btn-danger text-white px-8 py-4 rounded-xl font-semibold text-lg">
                            <i class="fas fa-redo mr-2"></i>
                            重置抽奖
                        </button>
                    </div>
                </div>

                <!-- 抽奖结果显示 -->
                <div v-if="lastResult" class="result-card rounded-xl p-8 text-center animate-bounce-in winner-animation">
                    <div class="text-6xl mb-4">🎉</div>
                    <div class="text-3xl font-bold text-gray-800 mb-2">恭喜</div>
                    <div class="text-4xl font-bold text-orange-600 mb-2">{{ lastResult.participant_name }}</div>
                    <div class="text-2xl text-gray-700">获得 <span class="font-bold text-orange-600">{{ lastResult.prize_name }}</span>!</div>
                    <div class="text-sm text-gray-500 mt-4">{{ lastResult.draw_time }}</div>
                </div>

                <!-- 批量结果显示 -->
                <div v-if="batchResults.length > 0" class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-xl font-bold mb-4 text-center">🎊 批量抽奖结果</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="result in batchResults" :key="result.id" 
                             class="bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-lg border-l-4 border-orange-400">
                            <div class="font-bold text-gray-800">{{ result.participant_name }}</div>
                            <div class="text-orange-600">{{ result.prize_name }}</div>
                            <div class="text-xs text-gray-500">{{ result.draw_time }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 奖品管理页面 -->
            <div v-if="activeTab === 'prizes'" class="space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 奖品列表 -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl card-shadow p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h2 class="text-xl font-bold">🏆 奖品列表</h2>
                                <button @click="loadPrizes" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-refresh"></i>
                                </button>
                            </div>
                            
                            <div v-if="prizes.length === 0" class="text-center py-8 text-gray-500">
                                暂无奖品，请添加奖品
                            </div>
                            
                            <div v-else class="space-y-3">
                                <div v-for="prize in prizes" :key="prize.id"
                                     class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2">
                                            <div class="font-semibold text-gray-800">{{ prize.name }}</div>
                                            <span v-if="prize.award_level" class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                {{ prize.award_level }}
                                            </span>
                                            <span v-if="prize.round_number > 0" class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                第{{ prize.round_number }}轮
                                            </span>
                                        </div>
                                        <div class="text-sm text-gray-600">
                                            数量: {{ prize.remaining }}/{{ prize.quantity }}
                                            <span v-if="prize.description" class="ml-2">| {{ prize.description }}</span>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button @click="editPrize(prize)" class="text-blue-600 hover:text-blue-800">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button @click="deletePrize(prize.id)" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 奖品编辑 -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-xl card-shadow p-6">
                            <h3 class="text-lg font-bold mb-4">{{ editingPrize ? '编辑奖品' : '添加奖品' }}</h3>
                            
                            <form @submit.prevent="savePrize" class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">奖品名称</label>
                                    <input v-model="prizeForm.name" type="text" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">数量</label>
                                        <input v-model.number="prizeForm.quantity" type="number" min="1" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">轮次</label>
                                        <input v-model.number="prizeForm.round_number" type="number" min="0" placeholder="0=任意轮次"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">奖项级别</label>
                                    <input v-model="prizeForm.award_level" type="text" placeholder="如：一等奖、二等奖"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">描述（可选）</label>
                                    <textarea v-model="prizeForm.description" rows="2"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                                </div>

                                <div class="flex space-x-2">
                                    <button type="submit" class="flex-1 btn-primary text-white py-2 px-4 rounded-lg font-medium">
                                        {{ editingPrize ? '更新' : '添加' }}
                                    </button>
                                    <button v-if="editingPrize" @click="cancelEdit" type="button"
                                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium">
                                        取消
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 人员管理页面 -->
            <div v-if="activeTab === 'participants'" class="space-y-6">
                <!-- 导入区域 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h2 class="text-xl font-bold mb-4">👥 人员管理</h2>
                    
                    <div class="flex flex-wrap gap-4 mb-6">
                        <label class="btn-primary text-white px-6 py-3 rounded-lg font-medium cursor-pointer hover:opacity-90">
                            <i class="fas fa-upload mr-2"></i>
                            批量导入
                            <input type="file" @change="uploadFile" accept=".csv,.xlsx,.xls" class="hidden">
                        </label>
                        
                        <button @click="clearParticipants" class="btn-danger text-white px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-trash mr-2"></i>
                            清空列表
                        </button>
                        
                        <button @click="loadParticipants" class="btn-secondary text-gray-800 px-6 py-3 rounded-lg font-medium">
                            <i class="fas fa-refresh mr-2"></i>
                            刷新列表
                        </button>
                    </div>
                    
                    <div class="text-sm text-gray-600">
                        <p>支持CSV和Excel文件格式</p>
                        <p>文件应包含"姓名"列，可选"电话"、"邮箱"、"部门"列</p>
                    </div>
                </div>

                <!-- 参与者列表 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold">参与者列表 ({{ participants.length }})</h3>
                        <div class="text-sm text-gray-600">
                            可参与: {{ participants.filter(p => !p.is_drawn).length }} | 
                            已中奖: {{ participants.filter(p => p.is_drawn).length }}
                        </div>
                    </div>
                    
                    <div v-if="participants.length === 0" class="text-center py-8 text-gray-500">
                        暂无参与者，请导入参与者数据
                    </div>
                    
                    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="participant in participants" :key="participant.id" 
                             :class="['p-4 rounded-lg border-2', 
                                      participant.is_drawn ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200']">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-semibold text-gray-800">{{ participant.name }}</div>
                                    <div v-if="participant.department" class="text-sm text-gray-600">{{ participant.department }}</div>
                                    <div v-if="participant.phone" class="text-xs text-gray-500">{{ participant.phone }}</div>
                                </div>
                                <div :class="['text-2xl', participant.is_drawn ? 'text-red-500' : 'text-green-500']">
                                    {{ participant.is_drawn ? '❌' : '✅' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果查看页面 -->
            <div v-if="activeTab === 'results'" class="space-y-6">
                <!-- 统计信息 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl card-shadow p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ results.statistics.total_draws }}</div>
                        <div class="text-gray-600">总抽奖次数</div>
                    </div>
                    <div class="bg-white rounded-xl card-shadow p-6 text-center">
                        <div class="text-3xl font-bold text-green-600">{{ results.statistics.winner_count }}</div>
                        <div class="text-gray-600">中奖人数</div>
                    </div>
                    <div class="bg-white rounded-xl card-shadow p-6 text-center">
                        <div class="text-3xl font-bold text-purple-600">{{ Object.keys(results.statistics.prize_stats).length }}</div>
                        <div class="text-gray-600">奖品种类</div>
                    </div>
                </div>

                <!-- 奖品统计 -->
                <div v-if="Object.keys(results.statistics.prize_stats).length > 0" class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-bold mb-4">🏆 奖品分布</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div v-for="(count, prize) in results.statistics.prize_stats" :key="prize"
                             class="bg-gradient-to-r from-blue-100 to-purple-100 p-4 rounded-lg">
                            <div class="font-semibold text-gray-800">{{ prize }}</div>
                            <div class="text-2xl font-bold text-purple-600">{{ count }}</div>
                        </div>
                    </div>
                </div>

                <!-- 中奖记录 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold">🎊 中奖记录</h3>
                        <button @click="loadResults" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    
                    <div v-if="results.results.length === 0" class="text-center py-8 text-gray-500">
                        暂无抽奖记录
                    </div>
                    
                    <div v-else class="space-y-3 max-h-96 overflow-y-auto">
                        <div v-for="result in results.results.slice().reverse()" :key="result.id"
                             class="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border-l-4 border-orange-400">
                            <div>
                                <div class="font-semibold text-gray-800">{{ result.participant_name }}</div>
                                <div class="text-orange-600">{{ result.prize_name }}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-gray-600">第{{ result.round_number }}轮</div>
                                <div class="text-xs text-gray-500">{{ result.draw_time }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量抽奖模态框 -->
        <div v-if="showBatchModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-bold mb-4">批量抽奖</h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">抽奖数量</label>
                    <input v-model.number="batchCount" type="number" min="1" max="100" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex space-x-3">
                    <button @click="drawBatch" class="flex-1 btn-primary text-white py-2 px-4 rounded-lg font-medium">
                        开始抽奖
                    </button>
                    <button @click="showBatchModal = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium">
                        取消
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载提示 -->
        <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-xl p-6 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <div class="text-gray-600">处理中...</div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    activeTab: 'lottery',
                    loading: false,
                    showBatchModal: false,
                    batchCount: 5,

                    // 系统状态
                    status: {
                        prizes: { total: 0, available: 0 },
                        participants: { total: 0, available: 0 },
                        can_draw: false,
                        status_message: '加载中...',
                        rounds: [],
                        award_levels: []
                    },

                    // 抽奖选项
                    selectedRound: '',
                    selectedAwardLevel: '',
                    drawCount: 1,

                    // 选项卡
                    tabs: [
                        { id: 'lottery', name: '开始抽奖', icon: '🎲' },
                        { id: 'prizes', name: '奖品管理', icon: '🏆' },
                        { id: 'participants', name: '人员管理', icon: '👥' },
                        { id: 'results', name: '抽奖结果', icon: '📊' }
                    ],

                    // 抽奖结果
                    lastResult: null,
                    batchResults: [],

                    // 奖品管理
                    prizes: [],
                    editingPrize: null,
                    prizeForm: {
                        name: '',
                        quantity: 1,
                        description: '',
                        award_level: '',
                        round_number: 0
                    },

                    // 参与者管理
                    participants: [],

                    // 结果统计
                    results: {
                        results: [],
                        statistics: {
                            total_draws: 0,
                            winner_count: 0,
                            prize_stats: {}
                        }
                    }
                }
            },

            mounted() {
                this.loadStatus();
                this.loadPrizes();
                this.loadParticipants();
                this.loadResults();
            },

            methods: {
                // API调用
                async apiCall(url, options = {}) {
                    try {
                        const response = await axios({
                            url: `/api${url}`,
                            ...options
                        });
                        return response.data;
                    } catch (error) {
                        console.error('API调用失败:', error);
                        if (error.response && error.response.data) {
                            throw new Error(error.response.data.error || '请求失败');
                        }
                        throw new Error('网络错误');
                    }
                },

                // 显示消息
                showMessage(message, type = 'info') {
                    // 简单的消息提示，可以替换为更好的通知组件
                    alert(message);
                },

                // 加载系统状态
                async loadStatus() {
                    try {
                        const data = await this.apiCall('/status');
                        if (data.success) {
                            this.status = data.data;
                        }
                    } catch (error) {
                        console.error('加载状态失败:', error);
                    }
                },

                // 抽奖相关方法
                async drawSingle() {
                    this.loading = true;
                    this.lastResult = null;
                    this.batchResults = [];

                    try {
                        const requestData = {};
                        if (this.selectedRound) {
                            requestData.round_number = parseInt(this.selectedRound);
                        }
                        if (this.selectedAwardLevel) {
                            requestData.award_level = this.selectedAwardLevel;
                        }

                        const data = await this.apiCall('/lottery/draw', {
                            method: 'POST',
                            data: requestData
                        });

                        if (data.success) {
                            this.lastResult = data.data;
                            this.loadStatus();
                            this.loadResults();
                            this.loadParticipants();
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                async drawBatch() {
                    if (this.drawCount < 1 || this.drawCount > 100) {
                        this.showMessage('抽奖数量必须在1-100之间', 'error');
                        return;
                    }

                    this.loading = true;
                    this.lastResult = null;
                    this.batchResults = [];

                    try {
                        const requestData = { count: this.drawCount };
                        if (this.selectedRound) {
                            requestData.round_number = parseInt(this.selectedRound);
                        }
                        if (this.selectedAwardLevel) {
                            requestData.award_level = this.selectedAwardLevel;
                        }

                        const data = await this.apiCall('/lottery/batch', {
                            method: 'POST',
                            data: requestData
                        });

                        if (data.success) {
                            this.batchResults = data.data;
                            this.loadStatus();
                            this.loadResults();
                            this.loadParticipants();
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                async drawEntireRound() {
                    if (!this.selectedRound) {
                        this.showMessage('请选择要抽奖的轮次', 'error');
                        return;
                    }

                    if (!confirm(`确定要抽取第${this.selectedRound}轮的所有奖品吗？`)) {
                        return;
                    }

                    this.loading = true;
                    this.lastResult = null;
                    this.batchResults = [];

                    try {
                        const data = await this.apiCall(`/lottery/draw-round/${this.selectedRound}`, {
                            method: 'POST'
                        });

                        if (data.success) {
                            this.batchResults = data.data;
                            this.loadStatus();
                            this.loadResults();
                            this.loadParticipants();
                            this.showMessage(`第${this.selectedRound}轮抽奖完成，共抽出${data.data.length}个奖项`, 'success');
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                async resetLottery() {
                    if (!confirm('确定要重置所有抽奖记录吗？此操作不可撤销。')) {
                        return;
                    }

                    this.loading = true;
                    try {
                        const data = await this.apiCall('/lottery/reset', { method: 'POST' });
                        if (data.success) {
                            this.lastResult = null;
                            this.batchResults = [];
                            this.loadStatus();
                            this.loadResults();
                            this.loadParticipants();
                            this.showMessage('抽奖已重置', 'success');
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // 奖品管理方法
                async loadPrizes() {
                    try {
                        const data = await this.apiCall('/prizes');
                        if (data.success) {
                            this.prizes = data.data;
                        }
                    } catch (error) {
                        console.error('加载奖品失败:', error);
                    }
                },

                async savePrize() {
                    if (!this.prizeForm.name.trim()) {
                        this.showMessage('请输入奖品名称', 'error');
                        return;
                    }

                    if (this.prizeForm.quantity < 1) {
                        this.showMessage('奖品数量必须大于0', 'error');
                        return;
                    }

                    this.loading = true;
                    try {
                        let data;
                        if (this.editingPrize) {
                            data = await this.apiCall(`/prizes/${this.editingPrize.id}`, {
                                method: 'PUT',
                                data: this.prizeForm
                            });
                        } else {
                            data = await this.apiCall('/prizes', {
                                method: 'POST',
                                data: this.prizeForm
                            });
                        }

                        if (data.success) {
                            this.loadPrizes();
                            this.loadStatus();
                            this.resetPrizeForm();
                            this.showMessage(this.editingPrize ? '奖品更新成功' : '奖品添加成功', 'success');
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                editPrize(prize) {
                    this.editingPrize = prize;
                    this.prizeForm = {
                        name: prize.name,
                        quantity: prize.quantity,
                        description: prize.description || '',
                        award_level: prize.award_level || '',
                        round_number: prize.round_number || 0
                    };
                },

                cancelEdit() {
                    this.editingPrize = null;
                    this.resetPrizeForm();
                },

                resetPrizeForm() {
                    this.prizeForm = {
                        name: '',
                        quantity: 1,
                        description: '',
                        award_level: '',
                        round_number: 0
                    };
                    this.editingPrize = null;
                },

                async deletePrize(prizeId) {
                    if (!confirm('确定要删除这个奖品吗？')) {
                        return;
                    }

                    this.loading = true;
                    try {
                        const data = await this.apiCall(`/prizes/${prizeId}`, { method: 'DELETE' });
                        if (data.success) {
                            this.loadPrizes();
                            this.loadStatus();
                            this.showMessage('奖品删除成功', 'success');
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // 参与者管理方法
                async loadParticipants() {
                    try {
                        const data = await this.apiCall('/participants');
                        if (data.success) {
                            this.participants = data.data;
                        }
                    } catch (error) {
                        console.error('加载参与者失败:', error);
                    }
                },

                async uploadFile(event) {
                    const file = event.target.files[0];
                    if (!file) return;

                    const formData = new FormData();
                    formData.append('file', file);

                    this.loading = true;
                    try {
                        const response = await axios.post('/api/participants/upload', formData, {
                            headers: {
                                'Content-Type': 'multipart/form-data'
                            }
                        });

                        if (response.data.success) {
                            this.loadParticipants();
                            this.loadStatus();
                            this.showMessage(response.data.message, 'success');
                        } else {
                            this.showMessage(response.data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.response?.data?.error || '文件上传失败', 'error');
                    } finally {
                        this.loading = false;
                        event.target.value = ''; // 清空文件选择
                    }
                },

                async clearParticipants() {
                    if (!confirm('确定要清空所有参与者吗？')) {
                        return;
                    }

                    this.loading = true;
                    try {
                        const data = await this.apiCall('/participants/clear', { method: 'DELETE' });
                        if (data.success) {
                            this.loadParticipants();
                            this.loadStatus();
                            this.showMessage('参与者列表已清空', 'success');
                        } else {
                            this.showMessage(data.error, 'error');
                        }
                    } catch (error) {
                        this.showMessage(error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // 结果查看方法
                async loadResults() {
                    try {
                        const data = await this.apiCall('/lottery/results');
                        if (data.success) {
                            this.results = data.data;
                        }
                    } catch (error) {
                        console.error('加载结果失败:', error);
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
