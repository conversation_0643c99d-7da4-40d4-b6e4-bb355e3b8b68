"""
主应用程序
iPhone风格的抽奖系统界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from .prize_manager import PrizeManager
    from .participant_manager import ParticipantManager
    from .lottery_engine import LotteryEngine
    from .ui_components import *
except ImportError:
    from prize_manager import PrizeManager
    from participant_manager import ParticipantManager
    from lottery_engine import LotteryEngine
    from ui_components import *


class LotteryApp:
    """抽奖应用主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_managers()
        self.setup_ui()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("抽奖系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#F2F2F7')
        
        # 设置窗口居中
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
        
        # 设置最小窗口大小
        self.root.minsize(1000, 600)
    
    def setup_managers(self):
        """初始化管理器"""
        self.prize_manager = PrizeManager()
        self.participant_manager = ParticipantManager()
        self.lottery_engine = LotteryEngine(self.prize_manager, self.participant_manager)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主容器
        main_container = iOSFrame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = iOSLabel(main_container, text="🎉 抽奖系统", style="title")
        title_label.pack(pady=(0, 30))
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 配置选项卡样式
        style = ttk.Style()
        style.configure('TNotebook.Tab', padding=[20, 10])
        
        # 创建各个选项卡
        self.create_lottery_tab()
        self.create_prize_tab()
        self.create_participant_tab()
        self.create_results_tab()
    
    def create_lottery_tab(self):
        """创建抽奖选项卡"""
        lottery_frame = iOSFrame(self.notebook)
        self.notebook.add(lottery_frame, text="🎲 开始抽奖")
        
        # 抽奖区域
        lottery_container = iOSFrame(lottery_frame)
        lottery_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 抽奖标题
        lottery_title = iOSLabel(lottery_container, text="准备开始抽奖", style="headline")
        lottery_title.pack(pady=(0, 20))
        
        # 状态信息
        self.status_frame = iOSFrame(lottery_container)
        self.status_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.update_lottery_status()
        
        # 抽奖控制区域
        control_frame = iOSFrame(lottery_container)
        control_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 抽奖按钮
        self.draw_button = iOSButton(
            control_frame, 
            text="🎯 开始抽奖", 
            command=self.start_lottery,
            style="primary"
        )
        self.draw_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 批量抽奖按钮
        self.batch_draw_button = iOSButton(
            control_frame,
            text="🎪 批量抽奖",
            command=self.batch_lottery,
            style="secondary"
        )
        self.batch_draw_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重置按钮
        reset_button = iOSButton(
            control_frame,
            text="🔄 重置抽奖",
            command=self.reset_lottery,
            style="danger"
        )
        reset_button.pack(side=tk.RIGHT)
        
        # 结果显示区域
        result_frame = iOSFrame(lottery_container)
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        result_label = iOSLabel(result_frame, text="抽奖结果", style="headline")
        result_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 创建结果显示区域
        self.result_display = AnimatedLabel(
            result_frame,
            text="点击开始抽奖按钮进行抽奖",
            font=('SF Pro Display', 24, 'bold'),
            fg='#8E8E93',
            bg='#F2F2F7',
            justify=tk.CENTER
        )
        self.result_display.pack(expand=True, fill=tk.BOTH)
    
    def create_prize_tab(self):
        """创建奖品管理选项卡"""
        prize_frame = iOSFrame(self.notebook)
        self.notebook.add(prize_frame, text="🏆 奖品管理")
        
        # 左侧：奖品列表
        left_frame = iOSFrame(prize_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 10), pady=20)
        
        prize_list_label = iOSLabel(left_frame, text="奖品列表", style="headline")
        prize_list_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 奖品列表框架
        list_frame = iOSFrame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.prize_listbox = iOSListbox(list_frame)
        self.prize_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        prize_scrollbar = iOSScrollbar(list_frame, orient=tk.VERTICAL)
        prize_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.prize_listbox.config(yscrollcommand=prize_scrollbar.set)
        prize_scrollbar.config(command=self.prize_listbox.yview)
        
        # 右侧：奖品编辑
        right_frame = iOSFrame(prize_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 20), pady=20)
        
        edit_label = iOSLabel(right_frame, text="编辑奖品", style="headline")
        edit_label.pack(anchor=tk.W, pady=(0, 20))
        
        # 奖品名称
        name_label = iOSLabel(right_frame, text="奖品名称:", style="body")
        name_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.prize_name_entry = iOSEntry(right_frame, placeholder="请输入奖品名称")
        self.prize_name_entry.pack(fill=tk.X, pady=(0, 15))
        
        # 奖品数量
        quantity_label = iOSLabel(right_frame, text="奖品数量:", style="body")
        quantity_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.prize_quantity_entry = iOSEntry(right_frame, placeholder="请输入数量")
        self.prize_quantity_entry.pack(fill=tk.X, pady=(0, 15))
        
        # 奖品描述
        desc_label = iOSLabel(right_frame, text="奖品描述:", style="body")
        desc_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.prize_desc_entry = iOSEntry(right_frame, placeholder="请输入描述（可选）")
        self.prize_desc_entry.pack(fill=tk.X, pady=(0, 20))
        
        # 按钮区域
        button_frame = iOSFrame(right_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        add_prize_button = iOSButton(
            button_frame,
            text="➕ 添加奖品",
            command=self.add_prize,
            style="success"
        )
        add_prize_button.pack(fill=tk.X, pady=(0, 10))
        
        update_prize_button = iOSButton(
            button_frame,
            text="✏️ 更新奖品",
            command=self.update_prize,
            style="secondary"
        )
        update_prize_button.pack(fill=tk.X, pady=(0, 10))
        
        delete_prize_button = iOSButton(
            button_frame,
            text="🗑️ 删除奖品",
            command=self.delete_prize,
            style="danger"
        )
        delete_prize_button.pack(fill=tk.X)
        
        # 绑定列表选择事件
        self.prize_listbox.bind('<<ListboxSelect>>', self.on_prize_select)
        
        # 初始化奖品列表
        self.refresh_prize_list()
    
    def create_participant_tab(self):
        """创建参与者管理选项卡"""
        participant_frame = iOSFrame(self.notebook)
        self.notebook.add(participant_frame, text="👥 人员管理")
        
        # 上方：导入区域
        import_frame = iOSFrame(participant_frame)
        import_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        import_label = iOSLabel(import_frame, text="批量导入参与者", style="headline")
        import_label.pack(anchor=tk.W, pady=(0, 15))
        
        import_button_frame = iOSFrame(import_frame)
        import_button_frame.pack(fill=tk.X)
        
        csv_button = iOSButton(
            import_button_frame,
            text="📄 导入CSV文件",
            command=self.import_csv,
            style="primary"
        )
        csv_button.pack(side=tk.LEFT, padx=(0, 10))
        
        excel_button = iOSButton(
            import_button_frame,
            text="📊 导入Excel文件",
            command=self.import_excel,
            style="secondary"
        )
        excel_button.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_button = iOSButton(
            import_button_frame,
            text="🗑️ 清空列表",
            command=self.clear_participants,
            style="danger"
        )
        clear_button.pack(side=tk.RIGHT)
        
        # 下方：参与者列表
        list_frame = iOSFrame(participant_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(10, 20))
        
        list_label = iOSLabel(list_frame, text="参与者列表", style="headline")
        list_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 参与者列表框架
        participant_list_frame = iOSFrame(list_frame)
        participant_list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.participant_listbox = iOSListbox(participant_list_frame)
        self.participant_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        participant_scrollbar = iOSScrollbar(participant_list_frame, orient=tk.VERTICAL)
        participant_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.participant_listbox.config(yscrollcommand=participant_scrollbar.set)
        participant_scrollbar.config(command=self.participant_listbox.yview)
        
        # 初始化参与者列表
        self.refresh_participant_list()
    
    def create_results_tab(self):
        """创建结果查看选项卡"""
        results_frame = iOSFrame(self.notebook)
        self.notebook.add(results_frame, text="📊 抽奖结果")
        
        # 上方：统计信息
        stats_frame = iOSFrame(results_frame)
        stats_frame.pack(fill=tk.X, padx=20, pady=(20, 10))
        
        stats_label = iOSLabel(stats_frame, text="抽奖统计", style="headline")
        stats_label.pack(anchor=tk.W, pady=(0, 10))
        
        self.stats_display = iOSLabel(stats_frame, text="暂无抽奖记录", style="body")
        self.stats_display.pack(anchor=tk.W)
        
        # 中间：导出按钮
        export_frame = iOSFrame(results_frame)
        export_frame.pack(fill=tk.X, padx=20, pady=10)
        
        export_button = iOSButton(
            export_frame,
            text="📤 导出结果",
            command=self.export_results,
            style="secondary"
        )
        export_button.pack(side=tk.LEFT)
        
        # 下方：结果列表
        results_list_frame = iOSFrame(results_frame)
        results_list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(10, 20))
        
        results_list_label = iOSLabel(results_list_frame, text="中奖记录", style="headline")
        results_list_label.pack(anchor=tk.W, pady=(0, 10))
        
        # 结果列表框架
        results_display_frame = iOSFrame(results_list_frame)
        results_display_frame.pack(fill=tk.BOTH, expand=True)
        
        self.results_listbox = iOSListbox(results_display_frame)
        self.results_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        results_scrollbar = iOSScrollbar(results_display_frame, orient=tk.VERTICAL)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.results_listbox.config(yscrollcommand=results_scrollbar.set)
        results_scrollbar.config(command=self.results_listbox.yview)
        
        # 初始化结果显示
        self.refresh_results_display()

    def update_lottery_status(self):
        """更新抽奖状态信息"""
        # 清空状态框架
        for widget in self.status_frame.winfo_children():
            widget.destroy()

        # 获取统计信息
        total_prizes = len(self.prize_manager.get_all_prizes())
        available_prizes = len(self.prize_manager.get_available_prizes())
        total_participants = len(self.participant_manager.get_all_participants())
        available_participants = len(self.participant_manager.get_available_participants())

        # 创建状态标签
        prize_status = iOSLabel(
            self.status_frame,
            text=f"🏆 奖品: {available_prizes}/{total_prizes} 可用",
            style="subheadline"
        )
        prize_status.pack(side=tk.LEFT, padx=(0, 20))

        participant_status = iOSLabel(
            self.status_frame,
            text=f"👥 参与者: {available_participants}/{total_participants} 可抽奖",
            style="subheadline"
        )
        participant_status.pack(side=tk.LEFT)

        # 检查是否可以抽奖
        can_draw, message = self.lottery_engine.can_draw()
        if not can_draw:
            status_color = "#FF3B30"  # 红色
        else:
            status_color = "#34C759"  # 绿色

        status_label = iOSLabel(
            self.status_frame,
            text=f"状态: {message}",
            style="subheadline",
            fg=status_color
        )
        status_label.pack(side=tk.RIGHT)

    def start_lottery(self):
        """开始单次抽奖"""
        can_draw, message = self.lottery_engine.can_draw()
        if not can_draw:
            messagebox.showwarning("无法抽奖", message)
            return

        # 执行抽奖
        result = self.lottery_engine.draw_by_probability()
        if result:
            # 显示结果
            result_text = f"🎉 恭喜 {result.participant_name}\n获得 {result.prize_name}!"
            self.result_display.config(
                text=result_text,
                fg="#34C759",
                font=('SF Pro Display', 28, 'bold')
            )

            # 播放动画
            self.result_display.pulse()

            # 更新状态和列表
            self.update_lottery_status()
            self.refresh_results_display()

            messagebox.showinfo("抽奖结果", result_text)
        else:
            messagebox.showerror("抽奖失败", "抽奖过程中出现错误")

    def batch_lottery(self):
        """批量抽奖"""
        # 询问批量数量
        batch_size = simpledialog.askinteger(
            "批量抽奖",
            "请输入要抽取的数量:",
            minvalue=1,
            maxvalue=100
        )

        if not batch_size:
            return

        can_draw, message = self.lottery_engine.can_draw()
        if not can_draw:
            messagebox.showwarning("无法抽奖", message)
            return

        # 执行批量抽奖
        results = self.lottery_engine.draw_batch(batch_size)

        if results:
            result_text = f"🎊 批量抽奖完成!\n共抽出 {len(results)} 个奖项"
            self.result_display.config(
                text=result_text,
                fg="#007AFF",
                font=('SF Pro Display', 24, 'bold')
            )

            # 更新状态和列表
            self.update_lottery_status()
            self.refresh_results_display()

            # 显示详细结果
            detail_text = "\n".join([f"{r.participant_name} - {r.prize_name}" for r in results])
            messagebox.showinfo("批量抽奖结果", f"抽奖完成，共 {len(results)} 人中奖:\n\n{detail_text}")
        else:
            messagebox.showwarning("抽奖失败", "没有可抽取的奖品或参与者")

    def reset_lottery(self):
        """重置抽奖"""
        if messagebox.askyesno("确认重置", "确定要重置所有抽奖记录吗？此操作不可撤销。"):
            self.lottery_engine.reset_lottery()
            self.result_display.config(
                text="抽奖已重置\n点击开始抽奖按钮进行抽奖",
                fg="#8E8E93",
                font=('SF Pro Display', 24, 'normal')
            )
            self.update_lottery_status()
            self.refresh_results_display()
            messagebox.showinfo("重置完成", "抽奖记录已重置")

    def add_prize(self):
        """添加奖品"""
        name = self.prize_name_entry.get_value()
        quantity_str = self.prize_quantity_entry.get_value()
        description = self.prize_desc_entry.get_value()

        if not name:
            messagebox.showwarning("输入错误", "请输入奖品名称")
            return

        try:
            quantity = int(quantity_str) if quantity_str else 1
            if quantity <= 0:
                raise ValueError("数量必须大于0")
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的数量")
            return

        # 添加奖品
        self.prize_manager.add_prize(name, quantity, description=description)

        # 清空输入框
        self.prize_name_entry.delete(0, tk.END)
        self.prize_name_entry.insert(0, self.prize_name_entry.placeholder)
        self.prize_name_entry.config(fg=self.prize_name_entry.placeholder_color)

        self.prize_quantity_entry.delete(0, tk.END)
        self.prize_quantity_entry.insert(0, self.prize_quantity_entry.placeholder)
        self.prize_quantity_entry.config(fg=self.prize_quantity_entry.placeholder_color)

        self.prize_desc_entry.delete(0, tk.END)
        self.prize_desc_entry.insert(0, self.prize_desc_entry.placeholder)
        self.prize_desc_entry.config(fg=self.prize_desc_entry.placeholder_color)

        # 刷新列表
        self.refresh_prize_list()
        self.update_lottery_status()

        messagebox.showinfo("添加成功", f"奖品 '{name}' 已添加")

    def update_prize(self):
        """更新奖品"""
        selection = self.prize_listbox.curselection()
        if not selection:
            messagebox.showwarning("选择错误", "请先选择要更新的奖品")
            return

        # 获取选中的奖品
        index = selection[0]
        prizes = self.prize_manager.get_all_prizes()
        if index >= len(prizes):
            return

        prize = prizes[index]

        # 获取新的值
        name = self.prize_name_entry.get_value()
        quantity_str = self.prize_quantity_entry.get_value()
        description = self.prize_desc_entry.get_value()

        if not name:
            messagebox.showwarning("输入错误", "请输入奖品名称")
            return

        try:
            quantity = int(quantity_str) if quantity_str else prize.quantity
            if quantity <= 0:
                raise ValueError("数量必须大于0")
        except ValueError:
            messagebox.showwarning("输入错误", "请输入有效的数量")
            return

        # 更新奖品
        self.prize_manager.update_prize(
            prize.id,
            name=name,
            quantity=quantity,
            description=description
        )

        # 刷新列表
        self.refresh_prize_list()
        self.update_lottery_status()

        messagebox.showinfo("更新成功", f"奖品 '{name}' 已更新")

    def delete_prize(self):
        """删除奖品"""
        selection = self.prize_listbox.curselection()
        if not selection:
            messagebox.showwarning("选择错误", "请先选择要删除的奖品")
            return

        # 获取选中的奖品
        index = selection[0]
        prizes = self.prize_manager.get_all_prizes()
        if index >= len(prizes):
            return

        prize = prizes[index]

        if messagebox.askyesno("确认删除", f"确定要删除奖品 '{prize.name}' 吗？"):
            self.prize_manager.delete_prize(prize.id)
            self.refresh_prize_list()
            self.update_lottery_status()
            messagebox.showinfo("删除成功", f"奖品 '{prize.name}' 已删除")

    def on_prize_select(self, event):
        """奖品选择事件"""
        selection = self.prize_listbox.curselection()
        if not selection:
            return

        # 获取选中的奖品
        index = selection[0]
        prizes = self.prize_manager.get_all_prizes()
        if index >= len(prizes):
            return

        prize = prizes[index]

        # 填充编辑框
        self.prize_name_entry.delete(0, tk.END)
        self.prize_name_entry.insert(0, prize.name)
        self.prize_name_entry.config(fg=self.prize_name_entry.normal_color)

        self.prize_quantity_entry.delete(0, tk.END)
        self.prize_quantity_entry.insert(0, str(prize.quantity))
        self.prize_quantity_entry.config(fg=self.prize_quantity_entry.normal_color)

        self.prize_desc_entry.delete(0, tk.END)
        if prize.description:
            self.prize_desc_entry.insert(0, prize.description)
            self.prize_desc_entry.config(fg=self.prize_desc_entry.normal_color)
        else:
            self.prize_desc_entry.insert(0, self.prize_desc_entry.placeholder)
            self.prize_desc_entry.config(fg=self.prize_desc_entry.placeholder_color)

    def refresh_prize_list(self):
        """刷新奖品列表"""
        self.prize_listbox.delete(0, tk.END)
        prizes = self.prize_manager.get_all_prizes()

        for prize in prizes:
            display_text = f"{prize.name} (数量: {prize.remaining}/{prize.quantity})"
            self.prize_listbox.insert(tk.END, display_text)

    def import_csv(self):
        """导入CSV文件"""
        file_path = filedialog.askopenfilename(
            title="选择CSV文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                count = self.participant_manager.import_from_csv(file_path)
                self.refresh_participant_list()
                self.update_lottery_status()
                messagebox.showinfo("导入成功", f"成功导入 {count} 个参与者")
            except Exception as e:
                messagebox.showerror("导入失败", f"导入CSV文件失败: {str(e)}")

    def import_excel(self):
        """导入Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                count = self.participant_manager.import_from_excel(file_path)
                self.refresh_participant_list()
                self.update_lottery_status()
                messagebox.showinfo("导入成功", f"成功导入 {count} 个参与者")
            except Exception as e:
                messagebox.showerror("导入失败", f"导入Excel文件失败: {str(e)}")

    def clear_participants(self):
        """清空参与者列表"""
        if messagebox.askyesno("确认清空", "确定要清空所有参与者吗？"):
            self.participant_manager.clear_all_participants()
            self.refresh_participant_list()
            self.update_lottery_status()
            messagebox.showinfo("清空成功", "参与者列表已清空")

    def refresh_participant_list(self):
        """刷新参与者列表"""
        self.participant_listbox.delete(0, tk.END)
        participants = self.participant_manager.get_all_participants()

        for participant in participants:
            status = "❌" if participant.is_drawn else "✅"
            display_text = f"{status} {participant.name}"
            if participant.department:
                display_text += f" ({participant.department})"
            self.participant_listbox.insert(tk.END, display_text)

    def export_results(self):
        """导出抽奖结果"""
        if not self.lottery_engine.get_all_results():
            messagebox.showwarning("无数据", "没有抽奖结果可导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存抽奖结果",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                success = self.lottery_engine.export_results_to_csv(file_path)
                if success:
                    messagebox.showinfo("导出成功", f"抽奖结果已导出到: {file_path}")
                else:
                    messagebox.showerror("导出失败", "导出过程中出现错误")
            except Exception as e:
                messagebox.showerror("导出失败", f"导出失败: {str(e)}")

    def refresh_results_display(self):
        """刷新结果显示"""
        # 更新统计信息
        results = self.lottery_engine.get_all_results()
        winner_count = self.lottery_engine.get_winner_count()
        prize_stats = self.lottery_engine.get_prize_statistics()

        if results:
            stats_text = f"总抽奖次数: {len(results)} | 中奖人数: {winner_count}"
            if prize_stats:
                stats_text += "\n奖品分布: " + " | ".join([f"{name}: {count}" for name, count in prize_stats.items()])
        else:
            stats_text = "暂无抽奖记录"

        self.stats_display.config(text=stats_text)

        # 更新结果列表
        self.results_listbox.delete(0, tk.END)
        for result in reversed(results):  # 最新的在前面
            display_text = f"🏆 {result.participant_name} - {result.prize_name} ({result.draw_time})"
            self.results_listbox.insert(tk.END, display_text)

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        # 创建并运行应用
        app = LotteryApp()
        app.run()
    except ImportError as e:
        print(f"缺少必要的模块: {e}")
        print("请安装pandas模块: pip install pandas openpyxl")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
