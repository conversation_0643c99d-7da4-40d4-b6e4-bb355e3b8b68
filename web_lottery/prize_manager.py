"""
奖品管理模块
负责奖品的添加、编辑、删除和管理
"""

import json
import os
from typing import List, Dict, Any
from dataclasses import dataclass, asdict
import uuid


@dataclass
class Prize:
    """奖品数据类"""
    id: str
    name: str
    quantity: int
    remaining: int
    probability: float = 0.0
    description: str = ""
    award_level: str = ""  # 奖项名称/级别，如"一等奖"、"二等奖"等
    round_number: int = 0  # 指定轮次，0表示任意轮次

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.remaining > self.quantity:
            self.remaining = self.quantity


class PrizeManager:
    """奖品管理器"""
    
    def __init__(self, data_file: str = "prizes.json"):
        self.data_file = data_file
        self.prizes: List[Prize] = []
        self.load_prizes()
    
    def load_prizes(self):
        """从文件加载奖品数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.prizes = [Prize(**prize_data) for prize_data in data]
            except (json.JSONDecodeError, TypeError) as e:
                print(f"加载奖品数据失败: {e}")
                self.prizes = []
        else:
            self.prizes = []
    
    def save_prizes(self):
        """保存奖品数据到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump([asdict(prize) for prize in self.prizes], f, 
                         ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存奖品数据失败: {e}")
    
    def add_prize(self, name: str, quantity: int, probability: float = 0.0,
                  description: str = "", award_level: str = "", round_number: int = 0) -> Prize:
        """添加新奖品"""
        prize = Prize(
            id=str(uuid.uuid4()),
            name=name,
            quantity=quantity,
            remaining=quantity,
            probability=probability,
            description=description,
            award_level=award_level,
            round_number=round_number
        )
        self.prizes.append(prize)
        self.save_prizes()
        return prize
    
    def update_prize(self, prize_id: str, **kwargs) -> bool:
        """更新奖品信息"""
        for prize in self.prizes:
            if prize.id == prize_id:
                for key, value in kwargs.items():
                    if hasattr(prize, key):
                        setattr(prize, key, value)
                # 确保剩余数量不超过总数量
                if prize.remaining > prize.quantity:
                    prize.remaining = prize.quantity
                self.save_prizes()
                return True
        return False
    
    def delete_prize(self, prize_id: str) -> bool:
        """删除奖品"""
        for i, prize in enumerate(self.prizes):
            if prize.id == prize_id:
                del self.prizes[i]
                self.save_prizes()
                return True
        return False
    
    def get_prize(self, prize_id: str) -> Prize:
        """获取指定奖品"""
        for prize in self.prizes:
            if prize.id == prize_id:
                return prize
        return None
    
    def get_all_prizes(self) -> List[Prize]:
        """获取所有奖品"""
        return self.prizes.copy()
    
    def get_available_prizes(self) -> List[Prize]:
        """获取有库存的奖品"""
        return [prize for prize in self.prizes if prize.remaining > 0]
    
    def consume_prize(self, prize_id: str) -> bool:
        """消耗一个奖品（中奖时调用）"""
        for prize in self.prizes:
            if prize.id == prize_id and prize.remaining > 0:
                prize.remaining -= 1
                self.save_prizes()
                return True
        return False
    
    def reset_all_prizes(self):
        """重置所有奖品数量"""
        for prize in self.prizes:
            prize.remaining = prize.quantity
        self.save_prizes()
    
    def get_prizes_by_round(self, round_number: int) -> List[Prize]:
        """获取指定轮次的奖品"""
        return [prize for prize in self.prizes
                if prize.round_number == round_number or prize.round_number == 0]

    def get_available_prizes_by_round(self, round_number: int) -> List[Prize]:
        """获取指定轮次有库存的奖品"""
        return [prize for prize in self.get_prizes_by_round(round_number)
                if prize.remaining > 0]

    def get_prizes_by_award_level(self, award_level: str) -> List[Prize]:
        """获取指定奖项级别的奖品"""
        return [prize for prize in self.prizes if prize.award_level == award_level]

    def get_all_award_levels(self) -> List[str]:
        """获取所有奖项级别"""
        levels = set()
        for prize in self.prizes:
            if prize.award_level:
                levels.add(prize.award_level)
        return sorted(list(levels))

    def get_all_rounds(self) -> List[int]:
        """获取所有轮次"""
        rounds = set()
        for prize in self.prizes:
            if prize.round_number > 0:
                rounds.add(prize.round_number)
        return sorted(list(rounds))

    def calculate_probabilities(self):
        """自动计算奖品概率（基于数量）"""
        total_quantity = sum(prize.quantity for prize in self.prizes)
        if total_quantity > 0:
            for prize in self.prizes:
                prize.probability = prize.quantity / total_quantity
        self.save_prizes()
