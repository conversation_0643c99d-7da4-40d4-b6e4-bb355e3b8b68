#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键下载微信文章图片并生成PDF
"""

import sys
from wechat_image_downloader import WeChatImageDownloader
from images_to_pdf import ImagesToPDF


def main():
    """主函数"""
    print("🚀 微信文章图片下载 + PDF生成工具")
    print("=" * 60)
    
    # 获取URL
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = input("请输入微信公众号文章URL: ").strip()
        if not url:
            print("❌ 错误：未提供URL")
            return
    
    print(f"📄 目标URL: {url}")
    print()
    
    # 第一步：下载图片
    print("🔽 第一步：下载图片")
    print("-" * 30)
    
    downloader = WeChatImageDownloader(output_dir="wechat_images")
    success, total = downloader.download_all_images(url)
    
    if total == 0:
        print("❌ 未找到任何图片，程序结束")
        return
    
    if success == 0:
        print("❌ 所有图片下载失败，程序结束")
        return
    
    print(f"✅ 图片下载完成！成功下载 {success}/{total} 张图片")
    print()
    
    # 第二步：生成PDF
    print("📄 第二步：生成PDF")
    print("-" * 30)
    
    converter = ImagesToPDF()
    pdf_success = converter.create_pdf(
        folder_path="wechat_images",
        output_path=None,  # 自动生成文件名
        resize=True        # 自动缩放
    )
    
    if pdf_success:
        print()
        print("🎉 任务全部完成！")
        print("📊 最终统计：")
        print(f"   - 下载图片：{success}/{total} 张")
        print(f"   - PDF生成：成功")
        print("💡 提示：PDF文件已保存在当前目录")
    else:
        print("❌ PDF生成失败")
        print("💡 但图片已成功下载到wechat_images文件夹")


if __name__ == "__main__":
    main()
