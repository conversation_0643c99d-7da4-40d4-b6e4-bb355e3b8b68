# 图片转PDF工具使用说明

## 功能介绍
这个工具可以将文件夹中的所有图片按顺序合成为一个PDF文件，特别适用于将下载的微信公众号文章图片整理成PDF文档。

## 文件说明
- `images_to_pdf.py` - 完整功能的图片转PDF工具
- `create_pdf.py` - 简化版本，专门用于处理wechat_images文件夹

## 安装依赖
```bash
pip install Pillow
```

## 使用方法

### 方法1：快速转换（推荐）
```bash
python create_pdf.py
```
- 自动处理 `wechat_images` 文件夹中的所有图片
- 自动生成带时间戳的PDF文件名
- 自动缩放大图片以减小文件大小

### 方法2：完整功能
```bash
# 基本用法 - 处理默认文件夹
python images_to_pdf.py

# 指定文件夹
python images_to_pdf.py my_images

# 指定输出文件名
python images_to_pdf.py wechat_images -o "我的文章.pdf"

# 不缩放图片（保持原始大小）
python images_to_pdf.py wechat_images --no-resize
```

## 功能特点

### 1. 智能图片处理
- **格式支持**：支持 JPG, PNG, GIF, BMP, WebP, TIFF 等常见格式
- **透明度处理**：自动将透明背景转换为白色背景
- **顺序保持**：严格按照文件名顺序排列图片

### 2. 自动优化
- **图片缩放**：自动缩放过大的图片（默认最大1200x1600像素）
- **文件压缩**：优化PDF文件大小，平衡质量和体积
- **错误处理**：跳过损坏的图片文件，继续处理其他图片

### 3. 详细反馈
- **进度显示**：实时显示处理进度
- **统计信息**：显示处理成功/失败的图片数量
- **文件信息**：显示生成的PDF文件大小和路径

## 输出示例
```
🖼️  图片转PDF工具
==================================================
正在处理文件夹: wechat_images
找到 71 张图片
开始创建PDF: wechat_images_20250904_105104.pdf
处理图片 1/71: image_001.jpg
处理图片 2/71: image_002.jpg
...
正在生成PDF文件...
✅ PDF创建成功!
📁 输出文件: C:\...\wechat_images_20250904_105104.pdf
📊 统计信息:
   - 总图片数: 71
   - 成功处理: 71
   - 处理失败: 0
   - 文件大小: 7.5 MB

🎉 任务完成！
```

## 命令行参数详解

### images_to_pdf.py 参数
- `folder` - 图片文件夹路径（可选，默认为wechat_images）
- `-o, --output` - 指定输出PDF文件路径
- `--no-resize` - 不自动缩放大图片

### 使用示例
```bash
# 处理不同文件夹
python images_to_pdf.py downloaded_images

# 自定义输出文件名
python images_to_pdf.py wechat_images -o "微信文章_2024.pdf"

# 保持原始图片大小
python images_to_pdf.py wechat_images --no-resize -o "高清版本.pdf"
```

## 文件命名规则
- **自动命名**：`文件夹名_YYYYMMDD_HHMMSS.pdf`
- **示例**：`wechat_images_20250904_105104.pdf`

## 支持的图片格式
| 格式 | 扩展名 | 说明 |
|------|--------|------|
| JPEG | .jpg, .jpeg | 最常见的图片格式 |
| PNG | .png | 支持透明背景 |
| GIF | .gif | 动图会转为静态图片 |
| BMP | .bmp | Windows位图格式 |
| WebP | .webp | 现代Web图片格式 |
| TIFF | .tiff, .tif | 高质量图片格式 |

## 注意事项
1. **文件顺序**：图片按文件名字母顺序排列，建议使用数字编号（如image_001.jpg）
2. **内存使用**：处理大量高分辨率图片时可能占用较多内存
3. **文件大小**：启用自动缩放可以显著减小PDF文件大小
4. **错误处理**：损坏或无法读取的图片会被跳过，不会中断整个过程

## 故障排除

### 常见问题
1. **ModuleNotFoundError: No module named 'PIL'**
   ```bash
   pip install Pillow
   ```

2. **找不到图片文件**
   - 检查文件夹路径是否正确
   - 确认文件夹中包含支持的图片格式

3. **PDF文件过大**
   - 使用默认的自动缩放功能
   - 或手动指定更小的缩放尺寸

4. **某些图片无法处理**
   - 检查图片文件是否损坏
   - 尝试用其他软件打开图片验证

## 与图片下载器的配合使用
1. 使用 `wechat_image_downloader.py` 下载微信文章图片
2. 使用 `create_pdf.py` 将下载的图片合成PDF
3. 一键完成从网页到PDF的完整流程

## 技术实现
- **图片处理**：使用Pillow (PIL) 库
- **PDF生成**：利用Pillow的PDF保存功能
- **格式转换**：自动处理RGBA到RGB的转换
- **压缩优化**：设置合适的质量参数平衡文件大小和清晰度
