# 微信公众号文章图片下载 + PDF生成工具

## 🎯 功能概述
这套工具可以自动从微信公众号文章中下载所有图片，并将图片按顺序合成为PDF文件，实现从网页到PDF的一键转换。

## 📁 文件结构
```
├── wechat_image_downloader.py     # 图片下载器（完整版）
├── simple_image_downloader.py     # 图片下载器（简化版）
├── verify_order.py                # 图片顺序验证工具
├── images_to_pdf.py               # PDF生成器（完整版）
├── create_pdf.py                  # PDF生成器（简化版）
├── 一键下载并生成PDF.py           # 一键完成工具
├── requirements.txt               # 依赖库列表
├── 图片下载器使用说明.md          # 下载器详细说明
├── PDF合成工具使用说明.md         # PDF工具详细说明
└── 完整使用说明.md                # 本文件
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 一键使用（推荐）
```bash
# 方法1：命令行参数
python 一键下载并生成PDF.py "https://mp.weixin.qq.com/s/your-article-url"

# 方法2：交互式输入
python 一键下载并生成PDF.py
```

### 3. 分步使用
```bash
# 步骤1：下载图片
python wechat_image_downloader.py

# 步骤2：生成PDF
python create_pdf.py
```

## 🛠️ 详细使用方法

### 图片下载
| 脚本 | 用途 | 使用方法 |
|------|------|----------|
| `wechat_image_downloader.py` | 下载默认URL的图片 | `python wechat_image_downloader.py` |
| `simple_image_downloader.py` | 下载指定URL的图片 | `python simple_image_downloader.py "URL"` |
| `verify_order.py` | 预览图片顺序 | `python verify_order.py` |

### PDF生成
| 脚本 | 用途 | 使用方法 |
|------|------|----------|
| `create_pdf.py` | 快速生成PDF | `python create_pdf.py` |
| `images_to_pdf.py` | 完整功能PDF生成 | `python images_to_pdf.py [选项]` |

### 一键工具
| 脚本 | 用途 | 使用方法 |
|------|------|----------|
| `一键下载并生成PDF.py` | 下载+PDF生成 | `python 一键下载并生成PDF.py "URL"` |

## ⚙️ 高级选项

### 图片下载器选项
- **输出目录**：默认为 `wechat_images`
- **图片过滤**：自动过滤表情包、图标等
- **顺序保持**：严格按文章中的图片顺序
- **错误处理**：单张图片失败不影响其他图片

### PDF生成器选项
```bash
# 指定输入文件夹
python images_to_pdf.py my_images

# 指定输出文件名
python images_to_pdf.py wechat_images -o "我的文章.pdf"

# 不缩放图片（保持原始大小）
python images_to_pdf.py wechat_images --no-resize
```

## 📊 输出示例

### 下载阶段
```
正在获取页面内容: https://mp.weixin.qq.com/s/...
在内容区域中查找图片...
找到 71 张图片（按文章顺序）
正在下载: image_001.jpg
下载成功: wechat_images\image_001.jpg
...
下载完成！成功下载 71/71 张图片
```

### PDF生成阶段
```
🖼️  图片转PDF工具
找到 71 张图片
开始创建PDF: wechat_images_20250904_105104.pdf
处理图片 1/71: image_001.jpg
...
✅ PDF创建成功!
📁 输出文件: wechat_images_20250904_105104.pdf
📊 统计信息:
   - 总图片数: 71
   - 成功处理: 71
   - 处理失败: 0
   - 文件大小: 7.5 MB
```

## 🔧 配置说明

### 图片下载配置
- **请求头**：模拟浏览器访问，避免被拦截
- **延时设置**：每张图片下载间隔1秒
- **内容区域**：优先在文章主要内容区域查找图片
- **URL处理**：自动处理相对路径和懒加载

### PDF生成配置
- **图片缩放**：默认最大1200x1600像素
- **质量设置**：85%质量，平衡文件大小和清晰度
- **格式转换**：自动处理透明背景
- **文件命名**：`文件夹名_YYYYMMDD_HHMMSS.pdf`

## 🎯 使用场景

### 场景1：学习资料整理
```bash
# 下载教程文章的图片并生成PDF
python 一键下载并生成PDF.py "https://mp.weixin.qq.com/s/tutorial-url"
```

### 场景2：批量处理
```bash
# 处理多个文章
python simple_image_downloader.py "URL1"
python simple_image_downloader.py "URL2" 
python images_to_pdf.py downloaded_images -o "合集.pdf"
```

### 场景3：高质量保存
```bash
# 保持原始图片质量
python wechat_image_downloader.py
python images_to_pdf.py wechat_images --no-resize -o "高清版.pdf"
```

## ⚠️ 注意事项

### 使用限制
1. **网络环境**：某些图片可能需要特殊网络环境
2. **访问频率**：避免过于频繁的请求
3. **版权问题**：仅供个人学习研究使用
4. **文件大小**：大量高分辨率图片会产生较大的PDF文件

### 故障排除
1. **依赖问题**：确保安装了所有必需的库
2. **网络问题**：检查网络连接和URL有效性
3. **权限问题**：确保有文件写入权限
4. **内存问题**：处理大量图片时可能需要更多内存

## 🔄 更新日志

### v1.0 功能
- ✅ 基础图片下载功能
- ✅ PDF生成功能
- ✅ 图片顺序保持
- ✅ 自动图片优化
- ✅ 错误处理机制
- ✅ 一键操作工具

### 计划功能
- 🔄 批量URL处理
- 🔄 自定义图片过滤规则
- 🔄 PDF书签生成
- 🔄 图片水印添加

## 📞 技术支持
如果遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖库是否正确安装
3. 网络连接是否正常
4. 文件权限是否足够

## 📄 许可证
本工具仅供学习和研究使用，请遵守相关网站的使用条款和版权规定。
