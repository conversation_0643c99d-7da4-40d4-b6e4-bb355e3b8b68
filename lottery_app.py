#!/usr/bin/env python3
"""
抽奖系统主程序
iPhone风格的Python抽奖应用

使用方法:
    python lottery_app.py

功能特性:
- 奖品管理：自由设定奖品名称、数量、描述
- 人员管理：支持CSV、Excel批量导入参与者
- 抽奖功能：单次抽奖、批量抽奖、概率抽奖
- 结果管理：查看统计、导出结果
- iPhone风格UI：现代化界面设计

依赖模块:
- tkinter (Python内置)
- pandas (pip install pandas)
- openpyxl (pip install openpyxl)
"""

import sys
import os

# 添加lottery_system目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'lottery_system'))

def check_dependencies():
    """检查依赖模块"""
    missing_modules = []
    
    try:
        import tkinter
    except ImportError:
        missing_modules.append("tkinter")
    
    try:
        import pandas
    except ImportError:
        missing_modules.append("pandas")
    
    try:
        import openpyxl
    except ImportError:
        missing_modules.append("openpyxl")
    
    if missing_modules:
        print("❌ 缺少以下依赖模块:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n📦 请安装缺少的模块:")
        if "pandas" in missing_modules or "openpyxl" in missing_modules:
            print("   pip install pandas openpyxl")
        return False
    
    return True


def main():
    """主函数"""
    print("🎉 抽奖系统启动中...")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入并运行应用
        from lottery_system.main_app import LotteryApp
        
        print("✅ 依赖检查通过")
        print("🚀 正在启动抽奖系统...")
        
        app = LotteryApp()
        app.run()
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 确保所有依赖模块已正确安装")
        print("2. 检查Python版本是否支持tkinter")
        print("3. 重新安装pandas和openpyxl模块")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
