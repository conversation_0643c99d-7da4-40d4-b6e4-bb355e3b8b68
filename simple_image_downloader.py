#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版微信公众号图片下载器
使用方法：python simple_image_downloader.py [URL]
"""

import sys
from wechat_image_downloader import WeChatImageDownloader


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        # 如果没有提供URL，使用默认URL或提示用户输入
        url = input("请输入微信公众号文章URL: ").strip()
        if not url:
            print("错误：未提供URL")
            return
    
    print(f"准备下载URL: {url}")
    
    # 创建下载器实例
    downloader = WeChatImageDownloader(output_dir="downloaded_images")
    
    # 下载所有图片
    success, total = downloader.download_all_images(url)
    
    if total > 0:
        print(f"\n任务完成！成功率: {success/total*100:.1f}%")
        print(f"共找到 {total} 张图片，成功下载 {success} 张")
    else:
        print("\n未找到任何图片或页面访问失败")


if __name__ == "__main__":
    main()
