#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片转PDF工具
快速将wechat_images文件夹中的图片转换为PDF
"""

from images_to_pdf import ImagesToPDF
import sys


def main():
    """主函数"""
    print("🖼️  图片转PDF工具")
    print("=" * 50)
    
    # 创建转换器
    converter = ImagesToPDF()
    
    # 默认转换wechat_images文件夹
    folder_path = "wechat_images"
    
    print(f"正在处理文件夹: {folder_path}")
    
    # 执行转换
    success = converter.create_pdf(
        folder_path=folder_path,
        output_path=None,  # 自动生成文件名
        resize=True        # 自动缩放大图片
    )
    
    if success:
        print("\n🎉 PDF创建成功！")
        print("💡 提示：PDF文件已保存在当前目录")
    else:
        print("\n❌ PDF创建失败！")
        print("💡 请检查wechat_images文件夹是否存在且包含图片文件")
        sys.exit(1)


if __name__ == "__main__":
    main()
